"""FastAPI router with /generate and /healthz endpoints."""

import uuid
import asyncio
from typing import Dict, Any

import torch
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from .config import settings
from .models import GenerationRequest, GenerationResponse, HealthResponse
from .generation import initialize_pipelines, run_generation, get_model_info

# Configure logging
logger.add(
    "logs/app.log",
    rotation="100 MB",
    retention="7 days",
    level=settings.log_level,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)

# Create FastAPI app
app = FastAPI(
    title="ACT Image Generator Backend",
    description="GPU-efficient SDXL image generation service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Initialize pipelines on startup."""
    logger.info("Starting ACT Image Generator Backend...")
    logger.info(f"Settings: {settings.dict()}")
    
    try:
        await initialize_pipelines()
        logger.info("Backend startup complete")
    except Exception as e:
        logger.error(f"Failed to start backend: {e}")
        raise


@app.exception_handler(torch.cuda.OutOfMemoryError)
async def cuda_oom_handler(request: Request, exc: torch.cuda.OutOfMemoryError):
    """Handle CUDA out of memory errors."""
    logger.error(f"CUDA OOM error: {exc}")
    torch.cuda.empty_cache()
    return JSONResponse(
        status_code=507,
        content={
            "success": False,
            "error": "GPU out of memory. Please try again with smaller dimensions or fewer steps.",
            "error_type": "cuda_oom"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "error_type": "internal_error"
        }
    )


@app.get("/healthz", response_model=HealthResponse)
async def health_check():
    """Health check endpoint with model and GPU information."""
    try:
        model_info = get_model_info()
        return HealthResponse(
            status="healthy",
            **model_info
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.post("/generate", response_model=GenerationResponse)
async def generate_image(request: GenerationRequest):
    """Generate an image using SDXL pipeline."""
    generation_id = str(uuid.uuid4())[:8]
    
    logger.info(f"Received generation request {generation_id}")
    logger.debug(f"Request parameters: {request.dict()}")
    
    try:
        # Run generation with timeout
        base64_image, latency_ms, vram_peak_mb = await asyncio.wait_for(
            run_generation(request, generation_id),
            timeout=settings.timeout_seconds
        )
        
        response = GenerationResponse(
            success=True,
            image=base64_image,
            generation_id=generation_id,
            latency_ms=latency_ms,
            vram_peak_mb=vram_peak_mb,
            parameters=request
        )
        
        logger.info(f"Generation {generation_id} successful")
        return response
        
    except asyncio.TimeoutError:
        logger.error(f"Generation {generation_id} timed out after {settings.timeout_seconds}s")
        raise HTTPException(
            status_code=408,
            detail={
                "success": False,
                "error": f"Generation timed out after {settings.timeout_seconds} seconds",
                "error_type": "timeout"
            }
        )
    except torch.cuda.OutOfMemoryError:
        # This will be caught by the exception handler
        raise
    except Exception as e:
        logger.error(f"Generation {generation_id} failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(e),
                "error_type": "generation_error"
            }
        )


@app.get("/")
async def root():
    """Root endpoint with basic service information."""
    return {
        "service": "ACT Image Generator Backend",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "generate": "/generate",
            "health": "/healthz",
            "docs": "/docs"
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "backend.app.router:app",
        host=settings.host,
        port=settings.port,
        workers=settings.workers,
        log_level=settings.log_level.lower()
    )
