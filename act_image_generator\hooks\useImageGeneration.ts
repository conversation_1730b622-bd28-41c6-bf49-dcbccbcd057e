"use client";

import useSWRMutation from "swr/mutation";
import { SDXLParameters, GenerationResponse } from "@/types/sdxl";

// Fetcher function for SWR mutation
async function generateImage(url: string, { arg }: { arg: SDXLParameters }): Promise<GenerationResponse> {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
  
  if (!backendUrl) {
    throw new Error("Backend URL not configured. Please set NEXT_PUBLIC_BACKEND_URL environment variable.");
  }

  const response = await fetch(`${backendUrl}${url}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(arg),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

// Convert base64 to blob URL
export function base64ToBlobUrl(base64: string, mimeType: string = "image/png"): string {
  // Remove data URL prefix if present
  const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, "");
  
  // Convert base64 to binary
  const binaryString = atob(base64Data);
  const bytes = new Uint8Array(binaryString.length);
  
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  // Create blob and return URL
  const blob = new Blob([bytes], { type: mimeType });
  return URL.createObjectURL(blob);
}

// Custom hook for image generation
export function useImageGeneration() {
  const { trigger, isMutating, data, error, reset } = useSWRMutation(
    "/generate",
    generateImage,
    {
      // SWR options
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      onError: (error) => {
        console.error("Image generation error:", error);
      },
    }
  );

  // Process the response to handle base64 images
  const processedData = data ? {
    ...data,
    blobUrl: data.image ? base64ToBlobUrl(data.image) : data.image_url,
  } : null;

  return {
    generateImage: trigger,
    isGenerating: isMutating,
    data: processedData,
    error: error?.message || null,
    reset,
  };
}
