"""Configuration management using Pydantic settings."""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Model configuration
    base_ckpt: str = Field(
        default="https://huggingface.co/SG161222/RealVisXL_V5.0/resolve/main/RealVisXL_V5.0_fp16.safetensors",
        env="BASE_CKPT",
        description="Path or URL to base SDXL checkpoint"
    )
    
    # LoRA configuration
    lora_dir: Optional[str] = Field(
        default=None,
        env="LORA_DIR", 
        description="Directory containing LoRA weights"
    )
    lora_file: Optional[str] = Field(
        default=None,
        env="LORA_FILE",
        description="LoRA weights filename"
    )
    lora_weight: float = Field(
        default=0.4,
        env="LORA_WEIGHT",
        description="LoRA adapter weight"
    )
    
    # Server configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS", description="Number of workers (GPU is serial)")
    
    # Generation configuration
    timeout_seconds: int = Field(default=120, env="TIMEOUT_SECONDS")
    max_memory_gb: float = Field(default=8.0, env="MAX_MEMORY_GB")
    
    # Device configuration
    device: str = Field(default="cuda", env="DEVICE")
    dtype: str = Field(default="float16", env="DTYPE")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
