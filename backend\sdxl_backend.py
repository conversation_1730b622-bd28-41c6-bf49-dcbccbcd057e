#!/usr/bin/env python3
"""
Single-file SDXL backend for ACT Image Generator.
GPU-efficient implementation with base + refiner pipeline.
"""

import asyncio
import base64
import hashlib
import io
import os
import time
import uuid
import warnings
from contextlib import asynccontextmanager
from typing import Optional

import torch
import uvicorn
from diffusers import DiffusionPipeline, StableDiffusionXLImg2ImgPipeline, AutoencoderKL
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from PIL import Image
from pydantic import BaseModel, Field, validator

# Suppress warnings
warnings.filterwarnings("ignore")

# Configuration
DTYPE = torch.float16
DEVICE = "cuda"
BASE_CKPT = os.getenv("BASE_CKPT", "https://huggingface.co/SG161222/RealVisXL_V5.0/resolve/main/RealVisXL_V5.0_fp16.safetensors")
LORA_DIR = os.getenv("LORA_DIR")
LORA_FILE = os.getenv("LORA_FILE")
LORA_WEIGHT = float(os.getenv("LORA_WEIGHT", 0.4))

# Global variables
_base = None
_ref = None
_lock = asyncio.Lock()
_model_hash = None


class GenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=1000)
    negative_prompt: str = Field(default="out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature", max_length=500)
    width: int = Field(default=1024, ge=512, le=2048)
    height: int = Field(default=1024, ge=512, le=2048)
    steps: int = Field(default=50, ge=20, le=100)
    denoising_end: float = Field(default=0.72, ge=0.6, le=0.9)
    guidance_scale: float = Field(default=6.0, ge=1.0, le=10.0)
    guidance_rescale: float = Field(default=0.7, ge=0.0, le=1.0)
    seed: Optional[int] = Field(default=None, ge=0, le=999999)

    @validator('width', 'height')
    def validate_dimensions(cls, v):
        if v % 8 != 0:
            raise ValueError(f"Dimension must be a multiple of 8, got {v}")
        return v


class GenerationResponse(BaseModel):
    success: bool
    image: Optional[str] = None
    generation_id: str
    latency_ms: float
    vram_peak_mb: float
    error: Optional[str] = None


@asynccontextmanager
async def gpu_swap(base, ref):
    """Context manager for GPU memory management."""
    try:
        base.to("cpu")
        torch.cuda.empty_cache()
        ref.to(DEVICE)
        yield ref
    finally:
        ref.to("cpu")
        torch.cuda.empty_cache()
        base.to(DEVICE)


def image_to_base64(image: Image.Image) -> str:
    """Convert PIL Image to base64 PNG."""
    buffer = io.BytesIO()
    image.save(buffer, format="PNG", optimize=True)
    buffer.seek(0)
    return base64.b64encode(buffer.getvalue()).decode('utf-8')


async def initialize_models():
    """Initialize SDXL pipelines."""
    global _base, _ref, _model_hash
    
    if _base is not None:
        return
    
    logger.info("Initializing SDXL models...")
    
    # Load base model
    _base = DiffusionPipeline.from_single_file(
        BASE_CKPT, torch_dtype=DTYPE, variant="fp16", add_watermarker=False
    ).to(DEVICE)
    _base.enable_attention_slicing()
    
    # Replace VAE
    _base.vae = AutoencoderKL.from_pretrained(
        "stabilityai/sdxl-vae", torch_dtype=DTYPE
    ).to(DEVICE)
    
    # Load LoRA if specified
    if LORA_DIR and LORA_FILE:
        _base.load_lora_weights(LORA_DIR, weight_name=LORA_FILE, adapter_name="main-lora")
        _base.set_adapters(["main-lora"], [LORA_WEIGHT])
    
    # Load refiner (keep on CPU)
    _ref = StableDiffusionXLImg2ImgPipeline.from_pretrained(
        "stabilityai/stable-diffusion-xl-refiner-1.0",
        torch_dtype=DTYPE, variant="fp16", add_watermarker=False
    ).to("cpu")
    _ref.enable_attention_slicing()
    
    # Calculate model hash
    _model_hash = hashlib.sha256(BASE_CKPT.encode()).hexdigest()[:16]
    
    logger.info("Model initialization complete")


async def run_generation(cfg: GenerationRequest, gen_id: str):
    """Run SDXL generation."""
    async with _lock:
        start_time = time.time()
        torch.cuda.reset_peak_memory_stats()
        
        g = torch.Generator(device=DEVICE)
        if cfg.seed is not None:
            g.manual_seed(cfg.seed)
        
        # Base generation
        latents = _base(
            cfg.prompt,
            negative_prompt=cfg.negative_prompt,
            width=cfg.width, height=cfg.height,
            num_inference_steps=cfg.steps,
            denoising_end=cfg.denoising_end,
            guidance_scale=cfg.guidance_scale,
            guidance_rescale=cfg.guidance_rescale,
            generator=g,
            output_type="latent"
        ).images
        
        # Refiner
        async with gpu_swap(_base, _ref) as refiner:
            image = refiner(
                cfg.prompt,
                negative_prompt=cfg.negative_prompt,
                image=latents,
                denoising_start=cfg.denoising_end,
                num_inference_steps=max(20, cfg.steps//2),
                generator=g
            ).images[0]
        
        latency_ms = (time.time() - start_time) * 1000
        vram_peak_mb = torch.cuda.max_memory_allocated() / 1024 / 1024
        
        return image_to_base64(image), latency_ms, vram_peak_mb


# FastAPI app
app = FastAPI(title="SDXL Backend", version="1.0.0")
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"])


@app.on_event("startup")
async def startup():
    await initialize_models()


@app.get("/healthz")
async def health():
    return {
        "status": "healthy",
        "model_sha256": _model_hash,
        "cuda_available": torch.cuda.is_available(),
        "cuda_device_count": torch.cuda.device_count(),
        "cuda_device_name": torch.cuda.get_device_name() if torch.cuda.is_available() else None,
        "memory_allocated_mb": torch.cuda.memory_allocated() / 1024 / 1024 if torch.cuda.is_available() else 0,
        "memory_reserved_mb": torch.cuda.memory_reserved() / 1024 / 1024 if torch.cuda.is_available() else 0,
    }


@app.post("/generate", response_model=GenerationResponse)
async def generate(request: GenerationRequest):
    gen_id = str(uuid.uuid4())[:8]
    
    try:
        image_b64, latency_ms, vram_peak_mb = await asyncio.wait_for(
            run_generation(request, gen_id), timeout=120
        )
        
        return GenerationResponse(
            success=True,
            image=image_b64,
            generation_id=gen_id,
            latency_ms=latency_ms,
            vram_peak_mb=vram_peak_mb
        )
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return GenerationResponse(
            success=False,
            generation_id=gen_id,
            latency_ms=0,
            vram_peak_mb=0,
            error=str(e)
        )


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
