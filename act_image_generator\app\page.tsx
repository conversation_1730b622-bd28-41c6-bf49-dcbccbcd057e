"use client";

import { useState, useEffect } from "react";
import GenerateForm from "@/components/GenerateForm";
import ImagePreview from "@/components/ImagePreview";
import ErrorToast from "@/components/ErrorToast";
import { useImageGeneration } from "@/hooks/useImageGeneration";
import { SDXLParameters } from "@/types/sdxl";
import { saveParametersToStorage, loadParametersFromStorage } from "@/utils/storage";

export default function Home() {
  const [parameters, setParameters] = useState<SDXLParameters>(() => loadParametersFromStorage());
  const [showErrorToast, setShowErrorToast] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Use SWR mutation hook for image generation
  const { generateImage, isGenerating, data, error, reset } = useImageGeneration();

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
    // Load parameters from localStorage after mounting
    const savedParams = loadParametersFromStorage();
    setParameters(savedParams);
  }, []);

  // Save parameters to localStorage whenever they change
  useEffect(() => {
    if (mounted) {
      saveParametersToStorage(parameters);
    }
  }, [parameters, mounted]);

  // Show error toast when error changes
  useEffect(() => {
    if (error) {
      setShowErrorToast(true);
    }
  }, [error]);

  const handleGenerate = async (formData: SDXLParameters) => {
    try {
      // Reset any previous errors
      reset();

      // Trigger the SWR mutation
      await generateImage(formData);

      // Update parameters with successful generation
      setParameters(formData);
    } catch (err) {
      console.error("Generation error:", err);
      // Error is handled by SWR and available in the error state
    }
  };

  const handleErrorToastClose = () => {
    setShowErrorToast(false);
    reset(); // Clear SWR error state
  };

  // Don't render until mounted to avoid hydration issues
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ACT Image Generator
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Generate high-quality images using SDXL with customizable parameters
          </p>
        </header>

        <div className="grid gap-6" style={{ gridTemplateColumns: "420px 1fr" }}>
          {/* Left Column - Form (420px wide) */}
          <div className="space-y-6">
            <GenerateForm
              onSubmit={handleGenerate}
              isLoading={isGenerating}
            />
          </div>

          {/* Right Column - Image Preview (flexible) */}
          <div className="sticky top-4 h-fit">
            <ImagePreview
              imageUrl={data?.blobUrl || data?.image_url}
              isLoading={isGenerating}
              error={error}
            />
          </div>
        </div>
      </div>

      {/* Error Toast */}
      <ErrorToast
        description={error || ""}
        open={showErrorToast || !!error}
        onOpenChange={handleErrorToastClose}
      />
    </div>
  );
}
