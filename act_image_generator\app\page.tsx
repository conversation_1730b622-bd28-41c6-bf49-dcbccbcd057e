"use client";

import { useState, useEffect } from "react";
import { Wand2 } from "lucide-react";
import PromptInput from "@/components/PromptInput";
import DimensionSelector from "@/components/DimensionSelector";
import AdvancedControls from "@/components/AdvancedControls";
import ImagePreview from "@/components/ImagePreview";
import { SDXLParameters, GenerationResponse } from "@/types/sdxl";
import { saveParametersToStorage, loadParametersFromStorage } from "@/utils/storage";

export default function Home() {
  const [parameters, setParameters] = useState<SDXLParameters>(() => loadParametersFromStorage());
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string>();
  const [error, setError] = useState<string>();

  // Save parameters to localStorage whenever they change
  useEffect(() => {
    saveParametersToStorage(parameters);
  }, [parameters]);

  const updateParameters = (updates: Partial<SDXLParameters>) => {
    setParameters(prev => ({ ...prev, ...updates }));
  };

  const handleGenerate = async () => {
    if (!parameters.prompt.trim()) {
      setError("Please enter a prompt");
      return;
    }

    setIsGenerating(true);
    setError(undefined);
    setGeneratedImageUrl(undefined);

    try {
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(parameters),
      });

      const result: GenerationResponse = await response.json();

      if (result.success && result.image_url) {
        setGeneratedImageUrl(result.image_url);
      } else {
        setError(result.error || "Failed to generate image");
      }
    } catch (err) {
      setError("Network error. Please try again.");
      console.error("Generation error:", err);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ACT Image Generator
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Generate high-quality images using SDXL with customizable parameters
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Form */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Generation Parameters
              </h2>

              <div className="space-y-6">
                <PromptInput
                  label="Prompt"
                  value={parameters.prompt}
                  onChange={(value) => updateParameters({ prompt: value })}
                  placeholder="Describe the image you want to generate..."
                  rows={4}
                />

                <DimensionSelector
                  selectedWidth={parameters.width}
                  selectedHeight={parameters.height}
                  onChange={(width, height) => updateParameters({ width, height })}
                />

                <AdvancedControls
                  parameters={parameters}
                  onChange={updateParameters}
                />

                <button
                  onClick={handleGenerate}
                  disabled={isGenerating || !parameters.prompt.trim()}
                  className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
                >
                  <Wand2 className="w-5 h-5" />
                  <span>{isGenerating ? "Generating..." : "Generate Image"}</span>
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Image Preview */}
          <div>
            <ImagePreview
              imageUrl={generatedImageUrl}
              isLoading={isGenerating}
              error={error}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
