"use client";

import { useState } from "react";
import { Download, Copy, Check, Image as ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import LoadingSpinner from "./LoadingSpinner";

interface ImagePreviewProps {
  imageUrl?: string;
  isLoading: boolean;
  error?: string;
  className?: string;
}

export default function ImagePreview({
  imageUrl,
  isLoading,
  error,
  className
}: ImagePreviewProps) {
  const [copied, setCopied] = useState(false);

  const handleDownload = async () => {
    if (!imageUrl) return;

    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `generated-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Failed to download image:", error);
    }
  };

  const handleCopyToClipboard = async () => {
    if (!imageUrl) return;

    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ "image/png": blob })
      ]);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy image:", error);
    }
  };

  return (
    <div className={cn(
      "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-lg p-6",
      className
    )}>
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Generated Image
      </h2>

      <div className="aspect-square bg-gray-50 dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700 flex items-center justify-center overflow-hidden">
        {isLoading ? (
          <LoadingSpinner size="lg" text="Generating image..." />
        ) : error ? (
          <div className="text-center p-6">
            <ImageIcon className="w-12 h-12 text-red-400 mx-auto mb-3" />
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        ) : imageUrl ? (
          <img
            src={imageUrl}
            alt="Generated image"
            className="w-full h-full object-contain rounded-lg"
          />
        ) : (
          <div className="text-center p-6">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Your generated image will appear here
            </p>
          </div>
        )}
      </div>

      {imageUrl && !isLoading && !error && (
        <div className="flex space-x-3 mt-6">
          <button
            onClick={handleDownload}
            className="flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm"
          >
            <Download className="w-4 h-4" />
            <span>Download</span>
          </button>

          <button
            onClick={handleCopyToClipboard}
            className="flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm"
          >
            {copied ? (
              <>
                <Check className="w-4 h-4" />
                <span>Copied!</span>
              </>
            ) : (
              <>
                <Copy className="w-4 h-4" />
                <span>Copy</span>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
}
