export declare const EVENTS: {
    readonly BLUR: "blur";
    readonly FOCUS_OUT: "focusout";
    readonly CHANGE: "change";
};
export declare const VALIDATION_MODE: {
    readonly onBlur: "onBlur";
    readonly onChange: "onChange";
    readonly onSubmit: "onSubmit";
    readonly onTouched: "onTouched";
    readonly all: "all";
};
export declare const INPUT_VALIDATION_RULES: {
    readonly max: "max";
    readonly min: "min";
    readonly maxLength: "maxLength";
    readonly minLength: "minLength";
    readonly pattern: "pattern";
    readonly required: "required";
    readonly validate: "validate";
};
//# sourceMappingURL=constants.d.ts.map