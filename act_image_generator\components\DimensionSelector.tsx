import { DIMENSION_OPTIONS, DimensionOption } from "@/types/sdxl";

interface DimensionSelectorProps {
  selectedWidth: number;
  selectedHeight: number;
  onChange: (width: number, height: number) => void;
}

export default function DimensionSelector({
  selectedWidth,
  selectedHeight,
  onChange,
}: DimensionSelectorProps) {
  const selectedOption = DIMENSION_OPTIONS.find(
    (option) => option.width === selectedWidth && option.height === selectedHeight
  );

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Dimensions
      </label>
      <div className="grid grid-cols-1 gap-2">
        {DIMENSION_OPTIONS.map((option) => (
          <label
            key={option.label}
            className="flex items-center space-x-3 cursor-pointer"
          >
            <input
              type="radio"
              name="dimensions"
              value={`${option.width}x${option.height}`}
              checked={
                option.width === selectedWidth && option.height === selectedHeight
              }
              onChange={() => onChange(option.width, option.height)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {option.label}
            </span>
          </label>
        ))}
      </div>
    </div>
  );
}
