# ACT Image Generator Backend

GPU-efficient SDXL image generation service with FastAPI.

## Features

- **SDXL Base + Refiner Pipeline** for high-quality image generation
- **GPU Memory Optimization** - keeps VRAM usage under 8GB
- **LoRA Support** for custom model fine-tuning
- **Async Generation** with proper GPU resource management
- **Comprehensive Logging** with generation metrics
- **Health Monitoring** with GPU status reporting

## Quick Start

### Option 1: Single File (Recommended for Testing)

```bash
# Install dependencies
pip install -r requirements.txt

# Run single-file backend
python sdxl_backend.py
```

### Option 2: Structured Application

```bash
# Install dependencies
pip install -r requirements.txt

# Run with uvicorn
python -m uvicorn backend.app.router:app --host 0.0.0.0 --port 8000 --reload
```

### Option 3: Docker

```bash
# Build image
docker build -t act-backend .

# Run container (requires NVIDIA Docker runtime)
docker run --gpus all -p 8000:8000 act-backend
```

## Configuration

Set environment variables to customize the backend:

```bash
# Model configuration
export BASE_CKPT="path/to/your/model.safetensors"
export LORA_DIR="/path/to/lora/weights"
export LORA_FILE="your_lora.safetensors"
export LORA_WEIGHT="0.4"

# Server configuration
export HOST="0.0.0.0"
export PORT="8000"
export TIMEOUT_SECONDS="120"
export LOG_LEVEL="INFO"
```

## API Endpoints

### POST /generate

Generate an image using SDXL pipeline.

**Request Body:**
```json
{
  "prompt": "portrait photo of a man in a suit, professional lighting",
  "negative_prompt": "blurry, low quality, distorted",
  "width": 1024,
  "height": 1024,
  "steps": 50,
  "denoising_end": 0.72,
  "guidance_scale": 6.0,
  "guidance_rescale": 0.7,
  "seed": 42
}
```

**Response:**
```json
{
  "success": true,
  "image": "base64_encoded_png_data",
  "generation_id": "abc123",
  "latency_ms": 15420.5,
  "vram_peak_mb": 7234.2,
  "parameters": { ... }
}
```

### GET /healthz

Health check with GPU and model information.

**Response:**
```json
{
  "status": "healthy",
  "model_sha256": "abc123...",
  "cuda_available": true,
  "cuda_device_count": 1,
  "cuda_device_name": "NVIDIA Tesla T4",
  "memory_allocated_mb": 1024.5,
  "memory_reserved_mb": 2048.0
}
```

## Testing

```bash
# Test generation endpoint
curl -X POST http://localhost:8000/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "portrait photo of a man in a suit, professional lighting",
    "width": 1024,
    "height": 1024,
    "steps": 50
  }' > response.json

# Test health endpoint
curl http://localhost:8000/healthz
```

## GPU Memory Optimization

The backend implements several optimizations to keep VRAM usage under 8GB:

1. **FP16 Precision** - Uses half-precision weights
2. **Attention Slicing** - Reduces memory during attention computation
3. **CPU Offloading** - Moves models between CPU/GPU as needed
4. **Pipeline Swapping** - Base and refiner share GPU memory
5. **Memory Cleanup** - Explicit cache clearing between generations

## Production Deployment

### Google Cloud Run (GPU)

```bash
gcloud run deploy act-image-backend \
  --source=. \
  --region=us-central1 \
  --platform=managed \
  --memory=16Gi \
  --cpu=4 \
  --gpu-type="nvidia-tesla-t4" \
  --gpu-count=1 \
  --set-env-vars="BASE_CKPT=gs://your-bucket/model.safetensors" \
  --timeout=900s \
  --max-instances=1
```

### Docker Compose

```yaml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - BASE_CKPT=/models/RealVisXL_V5.0_fp16.safetensors
    volumes:
      - ./models:/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

## Requirements

- **GPU**: NVIDIA GPU with 8GB+ VRAM (Tesla T4, RTX 3070, etc.)
- **CUDA**: 11.8 or 12.x
- **Python**: 3.11+
- **Memory**: 16GB+ system RAM recommended

## Troubleshooting

### CUDA Out of Memory
- Reduce image dimensions (width/height)
- Decrease inference steps
- Enable more aggressive CPU offloading

### Model Loading Issues
- Verify BASE_CKPT path/URL is accessible
- Check internet connection for HuggingFace downloads
- Ensure sufficient disk space for model cache

### Performance Issues
- Monitor GPU utilization with `nvidia-smi`
- Check logs for memory allocation patterns
- Consider using smaller base models for faster inference
