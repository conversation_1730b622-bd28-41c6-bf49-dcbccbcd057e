/**
 * Basic component tests for ACT Image Generator
 * 
 * To run these tests, you would need to install testing dependencies:
 * npm install --save-dev @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
 * 
 * Then add to package.json:
 * "scripts": {
 *   "test": "jest",
 *   "test:watch": "jest --watch"
 * }
 */

import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SliderControl from '../components/SliderControl';
import DimensionSelector from '../components/DimensionSelector';
import SeedControl from '../components/SeedControl';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

describe('SliderControl', () => {
  it('renders with correct initial value', () => {
    const mockOnChange = jest.fn();
    render(
      <SliderControl
        label="Test Slider"
        value={50}
        min={0}
        max={100}
        step={1}
        onChange={mockOnChange}
      />
    );
    
    expect(screen.getByText('Test Slider')).toBeInTheDocument();
    expect(screen.getByText('50')).toBeInTheDocument();
  });

  it('calls onChange when slider value changes', () => {
    const mockOnChange = jest.fn();
    render(
      <SliderControl
        label="Test Slider"
        value={50}
        min={0}
        max={100}
        step={1}
        onChange={mockOnChange}
      />
    );
    
    const slider = screen.getByRole('slider');
    fireEvent.change(slider, { target: { value: '75' } });
    
    expect(mockOnChange).toHaveBeenCalledWith(75);
  });
});

describe('DimensionSelector', () => {
  it('renders all dimension options', () => {
    const mockOnChange = jest.fn();
    render(
      <DimensionSelector
        selectedWidth={1024}
        selectedHeight={1024}
        onChange={mockOnChange}
      />
    );
    
    expect(screen.getByText('512 × 512')).toBeInTheDocument();
    expect(screen.getByText('768 × 768')).toBeInTheDocument();
    expect(screen.getByText('1024 × 1024')).toBeInTheDocument();
  });

  it('selects correct default option', () => {
    const mockOnChange = jest.fn();
    render(
      <DimensionSelector
        selectedWidth={1024}
        selectedHeight={1024}
        onChange={mockOnChange}
      />
    );
    
    const radio1024 = screen.getByDisplayValue('1024x1024');
    expect(radio1024).toBeChecked();
  });
});

describe('SeedControl', () => {
  it('renders with empty input when no seed provided', () => {
    const mockOnChange = jest.fn();
    render(<SeedControl onChange={mockOnChange} />);
    
    const input = screen.getByPlaceholderText('Random');
    expect(input).toHaveValue('');
  });

  it('generates random seed when button clicked', () => {
    const mockOnChange = jest.fn();
    render(<SeedControl onChange={mockOnChange} />);
    
    const randomButton = screen.getByTitle('Generate random seed');
    fireEvent.click(randomButton);
    
    expect(mockOnChange).toHaveBeenCalledWith(expect.any(Number));
  });
});
