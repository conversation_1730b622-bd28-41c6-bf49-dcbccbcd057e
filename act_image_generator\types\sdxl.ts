export interface SDXLParameters {
  prompt: string;
  negative_prompt: string;
  width: number;
  height: number;
  num_inference_steps: number;
  denoising_end: number;
  guidance_scale: number;
  guidance_rescale: number;
  seed?: number;
}

export interface GenerationResponse {
  success: boolean;
  image_url?: string;
  error?: string;
}

export interface DimensionOption {
  label: string;
  width: number;
  height: number;
}

export const DIMENSION_OPTIONS: DimensionOption[] = [
  { label: "512 × 512", width: 512, height: 512 },
  { label: "768 × 768", width: 768, height: 768 },
  { label: "1024 × 1024", width: 1024, height: 1024 },
];

export const DEFAULT_PARAMETERS: SDXLParameters = {
  prompt: "",
  negative_prompt: "out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature",
  width: 1024,
  height: 1024,
  num_inference_steps: 50,
  denoising_end: 0.72,
  guidance_scale: 6.0,
  guidance_rescale: 0.7,
};
