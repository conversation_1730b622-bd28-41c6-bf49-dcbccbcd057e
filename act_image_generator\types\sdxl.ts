import { z } from "zod";

// Zod schema for SDXL parameters with validation constraints
export const SDXLParametersSchema = z.object({
  prompt: z
    .string()
    .min(1, "Prompt is required")
    .max(1000, "Prompt must be less than 1000 characters"),
  negative_prompt: z
    .string()
    .max(500, "Negative prompt must be less than 500 characters")
    .default("out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature"),
  width: z
    .number()
    .int()
    .min(512, "Width must be at least 512")
    .max(2048, "Width must be at most 2048"),
  height: z
    .number()
    .int()
    .min(512, "Height must be at least 512")
    .max(2048, "Height must be at most 2048"),
  num_inference_steps: z
    .number()
    .int()
    .min(20, "Inference steps must be at least 20")
    .max(100, "Inference steps must be at most 100")
    .default(50),
  denoising_end: z
    .number()
    .min(0.6, "Denoising end must be at least 0.6")
    .max(0.9, "Denoising end must be at most 0.9")
    .default(0.72),
  guidance_scale: z
    .number()
    .min(1, "Guidance scale must be at least 1")
    .max(10, "Guidance scale must be at most 10")
    .default(6.0),
  guidance_rescale: z
    .number()
    .min(0.0, "Guidance rescale must be at least 0.0")
    .max(1.0, "Guidance rescale must be at most 1.0")
    .default(0.7),
  seed: z
    .number()
    .int()
    .min(0, "Seed must be a positive integer")
    .max(999999, "Seed must be less than 1,000,000")
    .optional(),
});

// TypeScript type inferred from Zod schema
export type SDXLParameters = z.infer<typeof SDXLParametersSchema>;

// API Response types
export interface GenerationResponse {
  success: boolean;
  image?: string; // base64 encoded image
  image_url?: string; // URL to image
  error?: string;
}

export interface DimensionOption {
  label: string;
  width: number;
  height: number;
}

export const DIMENSION_OPTIONS: DimensionOption[] = [
  { label: "512 × 512", width: 512, height: 512 },
  { label: "768 × 768", width: 768, height: 768 },
  { label: "1024 × 1024", width: 1024, height: 1024 },
];

// Default values - we'll use a partial schema for defaults to allow empty prompt
export const DEFAULT_PARAMETERS: Omit<SDXLParameters, 'prompt'> & { prompt: string } = {
  prompt: "",
  negative_prompt: "out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature",
  width: 1024,
  height: 1024,
  num_inference_steps: 50,
  denoising_end: 0.72,
  guidance_scale: 6.0,
  guidance_rescale: 0.7,
};
