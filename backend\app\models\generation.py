"""Pydantic models for SDXL generation requests and responses."""

from typing import Optional
from pydantic import BaseModel, Field, validator


class GenerationRequest(BaseModel):
    """Request model for SDXL image generation."""
    
    prompt: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="Text prompt for image generation"
    )
    
    negative_prompt: str = Field(
        default="out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature",
        max_length=500,
        description="Negative prompt to avoid certain features"
    )
    
    width: int = Field(
        default=1024,
        ge=512,
        le=2048,
        description="Image width in pixels"
    )
    
    height: int = Field(
        default=1024,
        ge=512,
        le=2048,
        description="Image height in pixels"
    )
    
    num_inference_steps: int = Field(
        default=50,
        ge=20,
        le=100,
        alias="steps",
        description="Number of denoising steps"
    )
    
    denoising_end: float = Field(
        default=0.72,
        ge=0.6,
        le=0.9,
        description="When to stop denoising in the base model"
    )
    
    guidance_scale: float = Field(
        default=6.0,
        ge=1.0,
        le=10.0,
        description="How closely to follow the prompt"
    )
    
    guidance_rescale: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Rescale guidance to prevent over-saturation"
    )
    
    seed: Optional[int] = Field(
        default=None,
        ge=0,
        le=999999,
        description="Random seed for reproducible results"
    )
    
    @validator('width', 'height')
    def validate_dimensions(cls, v):
        """Ensure dimensions are multiples of 8 for SDXL."""
        if v % 8 != 0:
            raise ValueError(f"Dimension must be a multiple of 8, got {v}")
        return v
    
    class Config:
        allow_population_by_field_name = True
        schema_extra = {
            "example": {
                "prompt": "portrait photo of a man in a suit, professional lighting, high quality",
                "negative_prompt": "blurry, low quality, distorted",
                "width": 1024,
                "height": 1024,
                "steps": 50,
                "denoising_end": 0.72,
                "guidance_scale": 6.0,
                "guidance_rescale": 0.7,
                "seed": 42
            }
        }


class GenerationResponse(BaseModel):
    """Response model for SDXL image generation."""
    
    success: bool = Field(description="Whether generation was successful")
    image: Optional[str] = Field(
        default=None,
        description="Base64 encoded PNG image data"
    )
    generation_id: str = Field(description="Unique identifier for this generation")
    latency_ms: float = Field(description="Generation time in milliseconds")
    vram_peak_mb: float = Field(description="Peak VRAM usage in MB")
    parameters: GenerationRequest = Field(description="Parameters used for generation")
    error: Optional[str] = Field(default=None, description="Error message if generation failed")


class HealthResponse(BaseModel):
    """Response model for health check endpoint."""
    
    status: str = Field(description="Service status")
    model_sha256: str = Field(description="SHA256 hash of loaded model")
    cuda_available: bool = Field(description="Whether CUDA is available")
    cuda_device_count: int = Field(description="Number of CUDA devices")
    cuda_device_name: Optional[str] = Field(description="Name of primary CUDA device")
    memory_allocated_mb: float = Field(description="Current GPU memory allocation in MB")
    memory_reserved_mb: float = Field(description="Current GPU memory reservation in MB")
