import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import PromptInput from "./PromptInput";
import SliderControl from "./SliderControl";
import SeedControl from "./SeedControl";
import { SDXLParameters } from "@/types/sdxl";

interface AdvancedControlsProps {
  parameters: SDXLParameters;
  onChange: (updates: Partial<SDXLParameters>) => void;
}

export default function AdvancedControls({
  parameters,
  onChange,
}: AdvancedControlsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 rounded-lg"
      >
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Advanced Settings
        </span>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
      </button>
      
      {isExpanded && (
        <div className="p-4 space-y-6 border-t border-gray-200 dark:border-gray-700">
          <PromptInput
            label="Negative Prompt"
            value={parameters.negative_prompt}
            onChange={(value) => onChange({ negative_prompt: value })}
            placeholder="Describe what you don't want in the image..."
            rows={3}
          />
          
          <SliderControl
            label="Inference Steps"
            value={parameters.num_inference_steps}
            min={20}
            max={100}
            step={1}
            onChange={(value) => onChange({ num_inference_steps: value })}
            description="More steps = higher quality but slower generation"
          />
          
          <SliderControl
            label="Denoising End"
            value={parameters.denoising_end}
            min={0.6}
            max={0.9}
            step={0.01}
            onChange={(value) => onChange({ denoising_end: value })}
            description="Controls when to stop the denoising process"
          />
          
          <SliderControl
            label="Guidance Scale"
            value={parameters.guidance_scale}
            min={1}
            max={10}
            step={0.1}
            onChange={(value) => onChange({ guidance_scale: value })}
            description="How closely to follow the prompt"
          />
          
          <SliderControl
            label="Guidance Rescale"
            value={parameters.guidance_rescale}
            min={0.0}
            max={1.0}
            step={0.05}
            onChange={(value) => onChange({ guidance_rescale: value })}
            description="Rescales the guidance to prevent over-saturation"
          />
          
          <SeedControl
            seed={parameters.seed}
            onChange={(value) => onChange({ seed: value })}
          />
        </div>
      )}
    </div>
  );
}
