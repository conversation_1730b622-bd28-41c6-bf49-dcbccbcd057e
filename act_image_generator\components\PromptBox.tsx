"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface PromptBoxProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  rows?: number;
  className?: string;
  error?: string;
}

export default function PromptBox({
  label,
  value,
  onChange,
  placeholder,
  maxLength = 1000,
  rows = 4,
  className,
  error,
}: PromptBoxProps) {
  const characterCount = value.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {label}
        </label>
        <span
          className={cn(
            "text-xs font-mono",
            isOverLimit
              ? "text-red-600 dark:text-red-400"
              : isNearLimit
              ? "text-yellow-600 dark:text-yellow-400"
              : "text-gray-500 dark:text-gray-400"
          )}
        >
          {characterCount}/{maxLength}
        </span>
      </div>
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        rows={rows}
        maxLength={maxLength}
        className={cn(
          "w-full px-3 py-2 border rounded-lg shadow-sm resize-vertical transition-colors",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          "dark:bg-gray-800 dark:text-white dark:placeholder-gray-400",
          error
            ? "border-red-300 dark:border-red-600"
            : "border-gray-300 dark:border-gray-600",
          isOverLimit && "border-red-300 dark:border-red-600"
        )}
      />
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
}
