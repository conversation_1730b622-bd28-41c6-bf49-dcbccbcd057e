import { Shuffle } from "lucide-react";

interface SeedControlProps {
  seed?: number;
  onChange: (seed?: number) => void;
}

export default function SeedControl({ seed, onChange }: SeedControlProps) {
  const generateRandomSeed = () => {
    const randomSeed = Math.floor(Math.random() * 1000000);
    onChange(randomSeed);
  };

  const handleInputChange = (value: string) => {
    if (value === "") {
      onChange(undefined);
    } else {
      const numValue = parseInt(value);
      if (!isNaN(numValue)) {
        onChange(numValue);
      }
    }
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Seed (optional)
      </label>
      <div className="flex space-x-2">
        <input
          type="number"
          value={seed || ""}
          onChange={(e) => handleInputChange(e.target.value)}
          placeholder="Random"
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
        />
        <button
          type="button"
          onClick={generateRandomSeed}
          className="px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-md transition-colors duration-200 flex items-center justify-center"
          title="Generate random seed"
        >
          <Shuffle className="w-4 h-4 text-gray-600 dark:text-gray-300" />
        </button>
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400">
        Leave empty for random seed, or enter a number for reproducible results
      </p>
    </div>
  );
}
