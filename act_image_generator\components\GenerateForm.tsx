"use client";

import { useForm } from "react-hook-form";
import { useState } from "react";
import { Wand2, ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { SDXLParameters, DEFAULT_PARAMETERS, DIMENSION_OPTIONS } from "@/types/sdxl";
import PromptBox from "./PromptBox";
import ParamSlider from "./ParamSlider";

interface GenerateFormProps {
  onSubmit: (data: SDXLParameters) => void;
  isLoading: boolean;
  className?: string;
}

export default function GenerateForm({ 
  onSubmit, 
  isLoading, 
  className 
}: GenerateFormProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<SDXLParameters>({
    defaultValues: DEFAULT_PARAMETERS,
  });

  const watchedValues = watch();

  const handleFormSubmit = (data: SDXLParameters) => {
    onSubmit(data);
  };

  return (
    <div className={cn(
      "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-lg p-6",
      className
    )}>
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        Generation Parameters
      </h2>
      
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Prompt */}
        <PromptBox
          label="Prompt"
          value={watchedValues.prompt}
          onChange={(value) => setValue("prompt", value)}
          placeholder="Describe the image you want to generate..."
          maxLength={1000}
          rows={4}
          error={errors.prompt?.message}
        />

        {/* Dimensions */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Dimensions
          </label>
          <div className="grid grid-cols-1 gap-2">
            {DIMENSION_OPTIONS.map((option) => (
              <label
                key={option.label}
                className="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <input
                  type="radio"
                  value={`${option.width}x${option.height}`}
                  checked={
                    watchedValues.width === option.width && 
                    watchedValues.height === option.height
                  }
                  onChange={() => {
                    setValue("width", option.width);
                    setValue("height", option.height);
                  }}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Advanced Settings */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full px-4 py-3 flex items-center justify-between text-left bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Advanced Settings
            </span>
            {showAdvanced ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
          
          {showAdvanced && (
            <div className="p-4 space-y-6 border-t border-gray-200 dark:border-gray-700">
              {/* Negative Prompt */}
              <PromptBox
                label="Negative Prompt"
                value={watchedValues.negative_prompt}
                onChange={(value) => setValue("negative_prompt", value)}
                placeholder="Describe what you don't want in the image..."
                maxLength={500}
                rows={3}
              />
              
              {/* Inference Steps */}
              <ParamSlider
                label="Inference Steps"
                value={watchedValues.num_inference_steps}
                min={20}
                max={100}
                step={1}
                onChange={(value) => setValue("num_inference_steps", value)}
                description="More steps = higher quality but slower generation"
              />
              
              {/* Denoising End */}
              <ParamSlider
                label="Denoising End"
                value={watchedValues.denoising_end}
                min={0.6}
                max={0.9}
                step={0.01}
                onChange={(value) => setValue("denoising_end", value)}
                description="Controls when to stop the denoising process"
              />
              
              {/* Guidance Scale */}
              <ParamSlider
                label="Guidance Scale"
                value={watchedValues.guidance_scale}
                min={1}
                max={10}
                step={0.1}
                onChange={(value) => setValue("guidance_scale", value)}
                description="How closely to follow the prompt"
              />
              
              {/* Guidance Rescale */}
              <ParamSlider
                label="Guidance Rescale"
                value={watchedValues.guidance_rescale}
                min={0.0}
                max={1.0}
                step={0.05}
                onChange={(value) => setValue("guidance_rescale", value)}
                description="Rescales the guidance to prevent over-saturation"
              />
              
              {/* Seed */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Seed (optional)
                </label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    {...register("seed", { valueAsNumber: true })}
                    placeholder="Random"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
                  />
                  <button
                    type="button"
                    onClick={() => setValue("seed", Math.floor(Math.random() * 1000000))}
                    className="px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors duration-200 text-sm font-medium"
                  >
                    Random
                  </button>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Leave empty for random seed, or enter a number for reproducible results
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Generate Button */}
        <button
          type="submit"
          disabled={isLoading || !watchedValues.prompt.trim()}
          className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-colors duration-200 shadow-lg"
        >
          <Wand2 className="w-5 h-5" />
          <span>{isLoading ? "Generating..." : "Generate Image"}</span>
        </button>
      </form>
    </div>
  );
}
