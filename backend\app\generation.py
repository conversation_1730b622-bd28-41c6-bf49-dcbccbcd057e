"""Core SDXL generation pipeline with GPU efficiency optimizations."""

import asyncio
import hashlib
import io
import base64
import warnings
from contextlib import asynccontextmanager
from typing import Optional, <PERSON><PERSON>
from PIL import Image

import torch
from diffusers import DiffusionPipeline, StableDiffusionXLImg2ImgPipeline, Autoencoder<PERSON>L
from loguru import logger

from .config import settings
from .models import GenerationRequest

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Global configuration
DTYPE = torch.float16 if settings.dtype == "float16" else torch.float32
DEVICE = settings.device

# Global pipeline instances
_base_pipeline: Optional[DiffusionPipeline] = None
_refiner_pipeline: Optional[StableDiffusionXLImg2ImgPipeline] = None
_generation_lock = asyncio.Lock()
_model_sha256: Optional[str] = None


def _calculate_model_hash(model_path: str) -> str:
    """Calculate SHA256 hash of the model file."""
    try:
        if model_path.startswith("http"):
            # For remote models, use a placeholder hash
            return hashlib.sha256(model_path.encode()).hexdigest()[:16]
        
        hash_sha256 = hashlib.sha256()
        with open(model_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()[:16]
    except Exception as e:
        logger.warning(f"Could not calculate model hash: {e}")
        return "unknown"


@asynccontextmanager
async def gpu_swap(base_pipeline, refiner_pipeline):
    """Context manager for efficient GPU memory management during base/refiner swap."""
    try:
        # Move base to CPU and refiner to GPU
        logger.debug("Moving base pipeline to CPU and refiner to GPU")
        base_pipeline.to("cpu")
        torch.cuda.empty_cache()
        refiner_pipeline.to(DEVICE)
        yield refiner_pipeline
    finally:
        # Move refiner back to CPU and base back to GPU
        logger.debug("Moving refiner to CPU and base pipeline back to GPU")
        refiner_pipeline.to("cpu")
        torch.cuda.empty_cache()
        base_pipeline.to(DEVICE)


async def initialize_pipelines():
    """Initialize SDXL base and refiner pipelines with optimizations."""
    global _base_pipeline, _refiner_pipeline, _model_sha256
    
    if _base_pipeline is not None:
        logger.info("Pipelines already initialized")
        return
    
    logger.info("Initializing SDXL pipelines...")
    
    try:
        # Load base pipeline
        logger.info(f"Loading base model from: {settings.base_ckpt}")
        _base_pipeline = DiffusionPipeline.from_single_file(
            settings.base_ckpt,
            torch_dtype=DTYPE,
            variant="fp16" if DTYPE == torch.float16 else None,
            add_watermarker=False,
            use_safetensors=True
        ).to(DEVICE)
        
        # Enable memory optimizations
        _base_pipeline.enable_attention_slicing()
        _base_pipeline.enable_model_cpu_offload()
        
        # Replace VAE with official SDXL VAE for better quality
        logger.info("Loading SDXL VAE...")
        _base_pipeline.vae = AutoencoderKL.from_pretrained(
            "stabilityai/sdxl-vae",
            torch_dtype=DTYPE
        ).to(DEVICE)
        
        # Load LoRA if specified
        if settings.lora_dir and settings.lora_file:
            logger.info(f"Loading LoRA: {settings.lora_dir}/{settings.lora_file}")
            _base_pipeline.load_lora_weights(
                settings.lora_dir,
                weight_name=settings.lora_file,
                adapter_name="main-lora"
            )
            _base_pipeline.set_adapters(["main-lora"], [settings.lora_weight])
        
        # Load refiner pipeline (keep on CPU initially)
        logger.info("Loading SDXL refiner...")
        _refiner_pipeline = StableDiffusionXLImg2ImgPipeline.from_pretrained(
            "stabilityai/stable-diffusion-xl-refiner-1.0",
            torch_dtype=DTYPE,
            variant="fp16" if DTYPE == torch.float16 else None,
            add_watermarker=False,
            use_safetensors=True
        ).to("cpu")
        
        _refiner_pipeline.enable_attention_slicing()
        
        # Calculate model hash
        _model_sha256 = _calculate_model_hash(settings.base_ckpt)
        
        logger.info("Pipeline initialization complete")
        
    except Exception as e:
        logger.error(f"Failed to initialize pipelines: {e}")
        raise


def _image_to_base64(image: Image.Image) -> str:
    """Convert PIL Image to base64 encoded PNG string."""
    buffer = io.BytesIO()
    image.save(buffer, format="PNG", optimize=True)
    buffer.seek(0)
    image_data = buffer.getvalue()
    return base64.b64encode(image_data).decode('utf-8')


async def run_generation(request: GenerationRequest, generation_id: str) -> Tuple[str, float, float]:
    """
    Run SDXL generation with base + refiner pipeline.
    
    Returns:
        Tuple of (base64_image, latency_ms, vram_peak_mb)
    """
    if _base_pipeline is None or _refiner_pipeline is None:
        raise RuntimeError("Pipelines not initialized")
    
    async with _generation_lock:
        import time
        start_time = time.time()
        
        # Reset VRAM tracking
        torch.cuda.reset_peak_memory_stats()
        
        logger.info(f"Starting generation {generation_id} with prompt: {request.prompt[:50]}...")
        
        # Setup generator
        generator = torch.Generator(device=DEVICE)
        if request.seed is not None:
            generator.manual_seed(request.seed)
            logger.debug(f"Using seed: {request.seed}")
        
        try:
            # Base generation (to latents)
            logger.debug("Running base pipeline...")
            latents = _base_pipeline(
                prompt=request.prompt,
                negative_prompt=request.negative_prompt,
                width=request.width,
                height=request.height,
                num_inference_steps=request.num_inference_steps,
                denoising_end=request.denoising_end,
                guidance_scale=request.guidance_scale,
                guidance_rescale=request.guidance_rescale,
                generator=generator,
                output_type="latent"
            ).images
            
            # GPU swap and refiner
            logger.debug("Running refiner pipeline...")
            async with gpu_swap(_base_pipeline, _refiner_pipeline) as refiner:
                image = refiner(
                    prompt=request.prompt,
                    negative_prompt=request.negative_prompt,
                    image=latents,
                    denoising_start=request.denoising_end,
                    num_inference_steps=max(20, request.num_inference_steps // 2),
                    generator=generator
                ).images[0]
            
            # Convert to base64
            base64_image = _image_to_base64(image)
            
            # Calculate metrics
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            vram_peak_mb = torch.cuda.max_memory_allocated() / 1024 / 1024
            
            logger.info(f"Generation {generation_id} completed in {latency_ms:.1f}ms, peak VRAM: {vram_peak_mb:.1f}MB")
            
            return base64_image, latency_ms, vram_peak_mb
            
        except torch.cuda.OutOfMemoryError as e:
            logger.error(f"CUDA OOM during generation {generation_id}: {e}")
            torch.cuda.empty_cache()
            raise
        except Exception as e:
            logger.error(f"Generation {generation_id} failed: {e}")
            raise


def get_model_info() -> dict:
    """Get information about the loaded model and GPU status."""
    return {
        "model_sha256": _model_sha256 or "unknown",
        "cuda_available": torch.cuda.is_available(),
        "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        "cuda_device_name": torch.cuda.get_device_name() if torch.cuda.is_available() else None,
        "memory_allocated_mb": torch.cuda.memory_allocated() / 1024 / 1024 if torch.cuda.is_available() else 0,
        "memory_reserved_mb": torch.cuda.memory_reserved() / 1024 / 1024 if torch.cuda.is_available() else 0,
    }
