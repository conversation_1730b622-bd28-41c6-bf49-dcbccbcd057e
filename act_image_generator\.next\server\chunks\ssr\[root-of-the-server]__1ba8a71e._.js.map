{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/PromptInput.tsx"], "sourcesContent": ["interface PromptInputProps {\n  label: string;\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  rows?: number;\n}\n\nexport default function PromptInput({\n  label,\n  value,\n  onChange,\n  placeholder,\n  rows = 4,\n}: PromptInputProps) {\n  return (\n    <div className=\"space-y-2\">\n      <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n        {label}\n      </label>\n      <textarea\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        placeholder={placeholder}\n        rows={rows}\n        className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400 resize-vertical\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAQe,SAAS,YAAY,EAClC,KAAK,EACL,KAAK,EACL,QAAQ,EACR,WAAW,EACX,OAAO,CAAC,EACS;IACjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAEH,8OAAC;gBACC,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,aAAa;gBACb,MAAM;gBACN,WAAU;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/types/sdxl.ts"], "sourcesContent": ["export interface SDXLParameters {\n  prompt: string;\n  negative_prompt: string;\n  width: number;\n  height: number;\n  num_inference_steps: number;\n  denoising_end: number;\n  guidance_scale: number;\n  guidance_rescale: number;\n  seed?: number;\n}\n\nexport interface GenerationResponse {\n  success: boolean;\n  image_url?: string;\n  error?: string;\n}\n\nexport interface DimensionOption {\n  label: string;\n  width: number;\n  height: number;\n}\n\nexport const DIMENSION_OPTIONS: DimensionOption[] = [\n  { label: \"512 × 512\", width: 512, height: 512 },\n  { label: \"768 × 768\", width: 768, height: 768 },\n  { label: \"1024 × 1024\", width: 1024, height: 1024 },\n];\n\nexport const DEFAULT_PARAMETERS: SDXLParameters = {\n  prompt: \"\",\n  negative_prompt: \"out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature\",\n  width: 1024,\n  height: 1024,\n  num_inference_steps: 50,\n  denoising_end: 0.72,\n  guidance_scale: 6.0,\n  guidance_rescale: 0.7,\n};\n"], "names": [], "mappings": ";;;;AAwBO,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAa,OAAO;QAAK,QAAQ;IAAI;IAC9C;QAAE,OAAO;QAAa,OAAO;QAAK,QAAQ;IAAI;IAC9C;QAAE,OAAO;QAAe,OAAO;QAAM,QAAQ;IAAK;CACnD;AAEM,MAAM,qBAAqC;IAChD,QAAQ;IACR,iBAAiB;IACjB,OAAO;IACP,QAAQ;IACR,qBAAqB;IACrB,eAAe;IACf,gBAAgB;IAChB,kBAAkB;AACpB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/DimensionSelector.tsx"], "sourcesContent": ["import { DIMENSION_OPTIONS, DimensionOption } from \"@/types/sdxl\";\n\ninterface DimensionSelectorProps {\n  selectedWidth: number;\n  selectedHeight: number;\n  onChange: (width: number, height: number) => void;\n}\n\nexport default function DimensionSelector({\n  selectedWidth,\n  selectedHeight,\n  onChange,\n}: DimensionSelectorProps) {\n  const selectedOption = DIMENSION_OPTIONS.find(\n    (option) => option.width === selectedWidth && option.height === selectedHeight\n  );\n\n  return (\n    <div className=\"space-y-2\">\n      <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n        Dimensions\n      </label>\n      <div className=\"grid grid-cols-1 gap-2\">\n        {DIMENSION_OPTIONS.map((option) => (\n          <label\n            key={option.label}\n            className=\"flex items-center space-x-3 cursor-pointer\"\n          >\n            <input\n              type=\"radio\"\n              name=\"dimensions\"\n              value={`${option.width}x${option.height}`}\n              checked={\n                option.width === selectedWidth && option.height === selectedHeight\n              }\n              onChange={() => onChange(option.width, option.height)}\n              className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n              {option.label}\n            </span>\n          </label>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS,kBAAkB,EACxC,aAAa,EACb,cAAc,EACd,QAAQ,EACe;IACvB,MAAM,iBAAiB,6GAAA,CAAA,oBAAiB,CAAC,IAAI,CAC3C,CAAC,SAAW,OAAO,KAAK,KAAK,iBAAiB,OAAO,MAAM,KAAK;IAGlE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;0BAAuD;;;;;;0BAGxE,8OAAC;gBAAI,WAAU;0BACZ,6GAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,uBACtB,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE;gCACzC,SACE,OAAO,KAAK,KAAK,iBAAiB,OAAO,MAAM,KAAK;gCAEtD,UAAU,IAAM,SAAS,OAAO,KAAK,EAAE,OAAO,MAAM;gCACpD,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CACb,OAAO,KAAK;;;;;;;uBAdV,OAAO,KAAK;;;;;;;;;;;;;;;;AAqB7B", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/SliderControl.tsx"], "sourcesContent": ["interface SliderControlProps {\n  label: string;\n  value: number;\n  min: number;\n  max: number;\n  step: number;\n  onChange: (value: number) => void;\n  description?: string;\n}\n\nexport default function SliderControl({\n  label,\n  value,\n  min,\n  max,\n  step,\n  onChange,\n  description,\n}: SliderControlProps) {\n  return (\n    <div className=\"space-y-2\">\n      <div className=\"flex justify-between items-center\">\n        <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {label}\n        </label>\n        <span className=\"text-sm text-gray-500 dark:text-gray-400 font-mono\">\n          {value}\n        </span>\n      </div>\n      <input\n        type=\"range\"\n        min={min}\n        max={max}\n        step={step}\n        value={value}\n        onChange={(e) => onChange(parseFloat(e.target.value))}\n        className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider\"\n      />\n      {description && (\n        <p className=\"text-xs text-gray-500 dark:text-gray-400\">{description}</p>\n      )}\n      <style jsx>{`\n        .slider::-webkit-slider-thumb {\n          appearance: none;\n          height: 20px;\n          width: 20px;\n          border-radius: 50%;\n          background: #3b82f6;\n          cursor: pointer;\n          border: 2px solid #ffffff;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n        .slider::-moz-range-thumb {\n          height: 20px;\n          width: 20px;\n          border-radius: 50%;\n          background: #3b82f6;\n          cursor: pointer;\n          border: 2px solid #ffffff;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAUe,SAAS,cAAc,EACpC,KAAK,EACL,KAAK,EACL,GAAG,EACH,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,WAAW,EACQ;IACnB,qBACE,8OAAC;kDAAc;;0BACb,8OAAC;0DAAc;;kCACb,8OAAC;kEAAgB;kCACd;;;;;;kCAEH,8OAAC;kEAAe;kCACb;;;;;;;;;;;;0BAGL,8OAAC;gBACC,MAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;0DACzC;;;;;;YAEX,6BACC,8OAAC;0DAAY;0BAA4C;;;;;;;;;;;;;;;;AAyBjE", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/SeedControl.tsx"], "sourcesContent": ["import { Shuffle } from \"lucide-react\";\n\ninterface SeedControlProps {\n  seed?: number;\n  onChange: (seed?: number) => void;\n}\n\nexport default function SeedControl({ seed, onChange }: SeedControlProps) {\n  const generateRandomSeed = () => {\n    const randomSeed = Math.floor(Math.random() * 1000000);\n    onChange(randomSeed);\n  };\n\n  const handleInputChange = (value: string) => {\n    if (value === \"\") {\n      onChange(undefined);\n    } else {\n      const numValue = parseInt(value);\n      if (!isNaN(numValue)) {\n        onChange(numValue);\n      }\n    }\n  };\n\n  return (\n    <div className=\"space-y-2\">\n      <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n        Seed (optional)\n      </label>\n      <div className=\"flex space-x-2\">\n        <input\n          type=\"number\"\n          value={seed || \"\"}\n          onChange={(e) => handleInputChange(e.target.value)}\n          placeholder=\"Random\"\n          className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400\"\n        />\n        <button\n          type=\"button\"\n          onClick={generateRandomSeed}\n          className=\"px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-md transition-colors duration-200 flex items-center justify-center\"\n          title=\"Generate random seed\"\n        >\n          <Shuffle className=\"w-4 h-4 text-gray-600 dark:text-gray-300\" />\n        </button>\n      </div>\n      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n        Leave empty for random seed, or enter a number for reproducible results\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAoB;IACtE,MAAM,qBAAqB;QACzB,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC9C,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU,IAAI;YAChB,SAAS;QACX,OAAO;YACL,MAAM,WAAW,SAAS;YAC1B,IAAI,CAAC,MAAM,WAAW;gBACpB,SAAS;YACX;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;0BAAuD;;;;;;0BAGxE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,OAAO,QAAQ;wBACf,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,aAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGvB,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;;;;;;;AAK9D", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/AdvancedControls.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { ChevronDown, ChevronUp } from \"lucide-react\";\nimport PromptInput from \"./PromptInput\";\nimport SliderControl from \"./SliderControl\";\nimport SeedControl from \"./SeedControl\";\nimport { SDXLParameters } from \"@/types/sdxl\";\n\ninterface AdvancedControlsProps {\n  parameters: SDXLParameters;\n  onChange: (updates: Partial<SDXLParameters>) => void;\n}\n\nexport default function AdvancedControls({\n  parameters,\n  onChange,\n}: AdvancedControlsProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  return (\n    <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg\">\n      <button\n        type=\"button\"\n        onClick={() => setIsExpanded(!isExpanded)}\n        className=\"w-full px-4 py-3 flex items-center justify-between text-left bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 rounded-lg\"\n      >\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          Advanced Settings\n        </span>\n        {isExpanded ? (\n          <ChevronUp className=\"w-4 h-4 text-gray-500\" />\n        ) : (\n          <ChevronDown className=\"w-4 h-4 text-gray-500\" />\n        )}\n      </button>\n      \n      {isExpanded && (\n        <div className=\"p-4 space-y-6 border-t border-gray-200 dark:border-gray-700\">\n          <PromptInput\n            label=\"Negative Prompt\"\n            value={parameters.negative_prompt}\n            onChange={(value) => onChange({ negative_prompt: value })}\n            placeholder=\"Describe what you don't want in the image...\"\n            rows={3}\n          />\n          \n          <SliderControl\n            label=\"Inference Steps\"\n            value={parameters.num_inference_steps}\n            min={20}\n            max={100}\n            step={1}\n            onChange={(value) => onChange({ num_inference_steps: value })}\n            description=\"More steps = higher quality but slower generation\"\n          />\n          \n          <SliderControl\n            label=\"Denoising End\"\n            value={parameters.denoising_end}\n            min={0.6}\n            max={0.9}\n            step={0.01}\n            onChange={(value) => onChange({ denoising_end: value })}\n            description=\"Controls when to stop the denoising process\"\n          />\n          \n          <SliderControl\n            label=\"Guidance Scale\"\n            value={parameters.guidance_scale}\n            min={1}\n            max={10}\n            step={0.1}\n            onChange={(value) => onChange({ guidance_scale: value })}\n            description=\"How closely to follow the prompt\"\n          />\n          \n          <SliderControl\n            label=\"Guidance Rescale\"\n            value={parameters.guidance_rescale}\n            min={0.0}\n            max={1.0}\n            step={0.05}\n            onChange={(value) => onChange({ guidance_rescale: value })}\n            description=\"Rescales the guidance to prevent over-saturation\"\n          />\n          \n          <SeedControl\n            seed={parameters.seed}\n            onChange={(value) => onChange({ seed: value })}\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAQe,SAAS,iBAAiB,EACvC,UAAU,EACV,QAAQ,EACc;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,cAAc,CAAC;gBAC9B,WAAU;;kCAEV,8OAAC;wBAAK,WAAU;kCAAuD;;;;;;oBAGtE,2BACC,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;6CAErB,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAI1B,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,UAAW;wBACV,OAAM;wBACN,OAAO,WAAW,eAAe;wBACjC,UAAU,CAAC,QAAU,SAAS;gCAAE,iBAAiB;4BAAM;wBACvD,aAAY;wBACZ,MAAM;;;;;;kCAGR,8OAAC,4HAAA,CAAA,UAAa;wBACZ,OAAM;wBACN,OAAO,WAAW,mBAAmB;wBACrC,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,UAAU,CAAC,QAAU,SAAS;gCAAE,qBAAqB;4BAAM;wBAC3D,aAAY;;;;;;kCAGd,8OAAC,4HAAA,CAAA,UAAa;wBACZ,OAAM;wBACN,OAAO,WAAW,aAAa;wBAC/B,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,UAAU,CAAC,QAAU,SAAS;gCAAE,eAAe;4BAAM;wBACrD,aAAY;;;;;;kCAGd,8OAAC,4HAAA,CAAA,UAAa;wBACZ,OAAM;wBACN,OAAO,WAAW,cAAc;wBAChC,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,UAAU,CAAC,QAAU,SAAS;gCAAE,gBAAgB;4BAAM;wBACtD,aAAY;;;;;;kCAGd,8OAAC,4HAAA,CAAA,UAAa;wBACZ,OAAM;wBACN,OAAO,WAAW,gBAAgB;wBAClC,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,UAAU,CAAC,QAAU,SAAS;gCAAE,kBAAkB;4BAAM;wBACxD,aAAY;;;;;;kCAGd,8OAAC,0HAAA,CAAA,UAAW;wBACV,MAAM,WAAW,IAAI;wBACrB,UAAU,CAAC,QAAU,SAAS;gCAAE,MAAM;4BAAM;;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  text?: string;\n}\n\nexport default function LoadingSpinner({ size = \"md\", text }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"w-4 h-4\",\n    md: \"w-8 h-8\",\n    lg: \"w-12 h-12\",\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center space-y-2\">\n      <div\n        className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}\n      />\n      {text && (\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">{text}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAKe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,IAAI,EAAuB;IAC/E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;YAEvG,sBACC,8OAAC;gBAAE,WAAU;0BAA4C;;;;;;;;;;;;AAIjE", "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/ImagePreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Download, Copy, Check, Image as ImageIcon } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport LoadingSpinner from \"./LoadingSpinner\";\n\ninterface ImagePreviewProps {\n  imageUrl?: string;\n  isLoading: boolean;\n  error?: string;\n  className?: string;\n}\n\nexport default function ImagePreview({\n  imageUrl,\n  isLoading,\n  error,\n  className\n}: ImagePreviewProps) {\n  const [copied, setCopied] = useState(false);\n\n  const handleDownload = async () => {\n    if (!imageUrl) return;\n\n    try {\n      const response = await fetch(imageUrl);\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `generated-image-${Date.now()}.png`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (error) {\n      console.error(\"Failed to download image:\", error);\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!imageUrl) return;\n\n    try {\n      const response = await fetch(imageUrl);\n      const blob = await response.blob();\n      await navigator.clipboard.write([\n        new ClipboardItem({ \"image/png\": blob })\n      ]);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error(\"Failed to copy image:\", error);\n    }\n  };\n\n  return (\n    <div className={cn(\n      \"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-lg p-6\",\n      className\n    )}>\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n        Generated Image\n      </h2>\n\n      <div className=\"aspect-square bg-gray-50 dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700 flex items-center justify-center overflow-hidden\">\n        {isLoading ? (\n          <LoadingSpinner size=\"lg\" text=\"Generating image...\" />\n        ) : error ? (\n          <div className=\"text-center p-6\">\n            <ImageIcon className=\"w-12 h-12 text-red-400 mx-auto mb-3\" />\n            <p className=\"text-red-600 dark:text-red-400 text-sm\">{error}</p>\n          </div>\n        ) : imageUrl ? (\n          <img\n            src={imageUrl}\n            alt=\"Generated image\"\n            className=\"w-full h-full object-contain rounded-lg\"\n          />\n        ) : (\n          <div className=\"text-center p-6\">\n            <ImageIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-3\" />\n            <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n              Your generated image will appear here\n            </p>\n          </div>\n        )}\n      </div>\n\n      {imageUrl && !isLoading && !error && (\n        <div className=\"flex space-x-3 mt-6\">\n          <button\n            onClick={handleDownload}\n            className=\"flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm\"\n          >\n            <Download className=\"w-4 h-4\" />\n            <span>Download</span>\n          </button>\n\n          <button\n            onClick={handleCopyToClipboard}\n            className=\"flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm\"\n          >\n            {copied ? (\n              <>\n                <Check className=\"w-4 h-4\" />\n                <span>Copied!</span>\n              </>\n            ) : (\n              <>\n                <Copy className=\"w-4 h-4\" />\n                <span>Copy</span>\n              </>\n            )}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAce,SAAS,aAAa,EACnC,QAAQ,EACR,SAAS,EACT,KAAK,EACL,SAAS,EACS;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;YAChD,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;gBAC9B,IAAI,cAAc;oBAAE,aAAa;gBAAK;aACvC;YACD,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,mGACA;;0BAEA,8OAAC;gBAAG,WAAU;0BAA2D;;;;;;0BAIzE,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC,6HAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,MAAK;;;;;2BAC7B,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;2BAEvD,yBACF,8OAAC;oBACC,KAAK;oBACL,KAAI;oBACJ,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;;;;;;YAO7D,YAAY,CAAC,aAAa,CAAC,uBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;kCAGR,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAET,uBACC;;8CACE,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;yDAGR;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/utils/storage.ts"], "sourcesContent": ["import { SDXLParameters, DEFAULT_PARAMETERS } from \"@/types/sdxl\";\n\nconst STORAGE_KEY = \"act_image_generator_parameters\";\n\nexport function saveParametersToStorage(parameters: SDXLParameters): void {\n  if (typeof window !== \"undefined\") {\n    try {\n      localStorage.setItem(STORAGE_KEY, JSON.stringify(parameters));\n    } catch (error) {\n      console.error(\"Failed to save parameters to localStorage:\", error);\n    }\n  }\n}\n\nexport function loadParametersFromStorage(): SDXLParameters {\n  if (typeof window !== \"undefined\") {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEY);\n      if (stored) {\n        const parsed = JSON.parse(stored);\n        // Merge with defaults to ensure all properties exist\n        return { ...DEFAULT_PARAMETERS, ...parsed };\n      }\n    } catch (error) {\n      console.error(\"Failed to load parameters from localStorage:\", error);\n    }\n  }\n  return DEFAULT_PARAMETERS;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AAEb,SAAS,wBAAwB,UAA0B;IAChE,uCAAmC;;IAMnC;AACF;AAEO,SAAS;IACd,uCAAmC;;IAWnC;IACA,OAAO,6GAAA,CAAA,qBAAkB;AAC3B", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Wand2 } from \"lucide-react\";\nimport PromptInput from \"@/components/PromptInput\";\nimport DimensionSelector from \"@/components/DimensionSelector\";\nimport AdvancedControls from \"@/components/AdvancedControls\";\nimport ImagePreview from \"@/components/ImagePreview\";\nimport { SDXLParameters, GenerationResponse } from \"@/types/sdxl\";\nimport { saveParametersToStorage, loadParametersFromStorage } from \"@/utils/storage\";\n\nexport default function Home() {\n  const [parameters, setParameters] = useState<SDXLParameters>(() => loadParametersFromStorage());\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generatedImageUrl, setGeneratedImageUrl] = useState<string>();\n  const [error, setError] = useState<string>();\n\n  // Save parameters to localStorage whenever they change\n  useEffect(() => {\n    saveParametersToStorage(parameters);\n  }, [parameters]);\n\n  const updateParameters = (updates: Partial<SDXLParameters>) => {\n    setParameters(prev => ({ ...prev, ...updates }));\n  };\n\n  const handleGenerate = async () => {\n    if (!parameters.prompt.trim()) {\n      setError(\"Please enter a prompt\");\n      return;\n    }\n\n    setIsGenerating(true);\n    setError(undefined);\n    setGeneratedImageUrl(undefined);\n\n    try {\n      const response = await fetch(\"/api/generate\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(parameters),\n      });\n\n      const result: GenerationResponse = await response.json();\n\n      if (result.success && result.image_url) {\n        setGeneratedImageUrl(result.image_url);\n      } else {\n        setError(result.error || \"Failed to generate image\");\n      }\n    } catch (err) {\n      setError(\"Network error. Please try again.\");\n      console.error(\"Generation error:\", err);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n            ACT Image Generator\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Generate high-quality images using SDXL with customizable parameters\n          </p>\n        </header>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Left Column - Form */}\n          <div className=\"space-y-6\">\n            <div className=\"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                Generation Parameters\n              </h2>\n\n              <div className=\"space-y-6\">\n                <PromptInput\n                  label=\"Prompt\"\n                  value={parameters.prompt}\n                  onChange={(value) => updateParameters({ prompt: value })}\n                  placeholder=\"Describe the image you want to generate...\"\n                  rows={4}\n                />\n\n                <DimensionSelector\n                  selectedWidth={parameters.width}\n                  selectedHeight={parameters.height}\n                  onChange={(width, height) => updateParameters({ width, height })}\n                />\n\n                <AdvancedControls\n                  parameters={parameters}\n                  onChange={updateParameters}\n                />\n\n                <button\n                  onClick={handleGenerate}\n                  disabled={isGenerating || !parameters.prompt.trim()}\n                  className=\"w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200\"\n                >\n                  <Wand2 className=\"w-5 h-5\" />\n                  <span>{isGenerating ? \"Generating...\" : \"Generate Image\"}</span>\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Image Preview */}\n          <div>\n            <ImagePreview\n              imageUrl={generatedImageUrl}\n              isLoading={isGenerating}\n              error={error}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,IAAM,CAAA,GAAA,gHAAA,CAAA,4BAAyB,AAAD;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAEjC,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,gHAAA,CAAA,0BAAuB,AAAD,EAAE;IAC1B,GAAG;QAAC;KAAW;IAEf,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAChD;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,MAAM,CAAC,IAAI,IAAI;YAC7B,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,OAAO,OAAO,IAAI,OAAO,SAAS,EAAE;gBACtC,qBAAqB,OAAO,SAAS;YACvC,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAKlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,UAAW;gDACV,OAAM;gDACN,OAAO,WAAW,MAAM;gDACxB,UAAU,CAAC,QAAU,iBAAiB;wDAAE,QAAQ;oDAAM;gDACtD,aAAY;gDACZ,MAAM;;;;;;0DAGR,8OAAC,gIAAA,CAAA,UAAiB;gDAChB,eAAe,WAAW,KAAK;gDAC/B,gBAAgB,WAAW,MAAM;gDACjC,UAAU,CAAC,OAAO,SAAW,iBAAiB;wDAAE;wDAAO;oDAAO;;;;;;0DAGhE,8OAAC,+HAAA,CAAA,UAAgB;gDACf,YAAY;gDACZ,UAAU;;;;;;0DAGZ,8OAAC;gDACC,SAAS;gDACT,UAAU,gBAAgB,CAAC,WAAW,MAAM,CAAC,IAAI;gDACjD,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAM,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhD,8OAAC;sCACC,cAAA,8OAAC,2HAAA,CAAA,UAAY;gCACX,UAAU;gCACV,WAAW;gCACX,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}]}