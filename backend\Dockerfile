# GPU-enabled SDXL backend container
FROM nvidia/cuda:12.2.2-cudnn9-runtime-ubuntu22.04

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1 \
    PYTHONPATH="/app"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    python3.11 \
    python3.11-venv \
    python3.11-dev \
    python3-pip \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN python3.11 -m pip install --upgrade pip && \
    python3.11 -m pip install -r requirements.txt

# Copy application code
COPY backend ./backend

# Create logs directory
RUN mkdir -p logs

# Set default environment variables
ENV BASE_CKPT="https://huggingface.co/SG161222/RealVisXL_V5.0/resolve/main/RealVisXL_V5.0_fp16.safetensors"
ENV HOST="0.0.0.0"
ENV PORT="8000"
ENV WORKERS="1"
ENV LOG_LEVEL="INFO"
ENV TIMEOUT_SECONDS="120"

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/healthz || exit 1

# Run the application
CMD ["python3.11", "-m", "uvicorn", "backend.app.router:app", "--host", "0.0.0.0", "--port", "8000"]
