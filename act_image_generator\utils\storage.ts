import { SDXLParameters, DEFAULT_PARAMETERS } from "@/types/sdxl";

const STORAGE_KEY = "act_image_generator_parameters";

export function saveParametersToStorage(parameters: SDXLParameters): void {
  if (typeof window !== "undefined") {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(parameters));
    } catch (error) {
      console.error("Failed to save parameters to localStorage:", error);
    }
  }
}

export function loadParametersFromStorage(): SDXLParameters {
  if (typeof window !== "undefined") {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Merge with defaults to ensure all properties exist
        return { ...DEFAULT_PARAMETERS, ...parsed };
      }
    } catch (error) {
      console.error("Failed to load parameters from localStorage:", error);
    }
  }
  return DEFAULT_PARAMETERS;
}
