# ACT Image Generator

A React/Next.js 15 frontend for generating high-quality images using SDXL with customizable parameters.

## ✨ Features

- **Intuitive UI**: Clean, responsive interface with form on the left and image preview on the right
- **SDXL Parameters**: Full control over all SDXL generation parameters
- **Advanced Controls**: Collapsible advanced settings section
- **Real-time Preview**: Immediate image preview with download and copy functionality
- **Persistent Settings**: Automatically saves your last used parameters to localStorage
- **Loading States**: Progress indicators and disabled states during generation

## 🎛️ Supported Parameters

| Parameter | UI Control | Default | Range |
|-----------|------------|---------|-------|
| `prompt` | Multiline text box | *empty* | - |
| `negative_prompt` | Multiline text box (Advanced) | "out of frame..." | - |
| `width` / `height` | Radio buttons | **1024×1024** | 512×512, 768×768, 1024×1024 |
| `num_inference_steps` | Slider | **50** | 20-100 |
| `denoising_end` | Slider | **0.72** | 0.60-0.90 |
| `guidance_scale` | Slider | **6.0** | 1-10 |
| `guidance_rescale` | Slider | **0.7** | 0.0-1.0 |
| `seed` | Number input + Random button | **random** | Optional |

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
act_image_generator/
├── app/
│   ├── api/generate/route.ts    # API endpoint for image generation
│   ├── layout.tsx               # Root layout
│   ├── page.tsx                 # Main application page
│   └── globals.css              # Global styles
├── components/
│   ├── AdvancedControls.tsx     # Collapsible advanced settings
│   ├── DimensionSelector.tsx    # Radio buttons for image dimensions
│   ├── ImagePreview.tsx         # Image display with download/copy
│   ├── LoadingSpinner.tsx       # Loading indicator
│   ├── PromptInput.tsx          # Multiline text input
│   ├── SeedControl.tsx          # Seed input with random button
│   └── SliderControl.tsx        # Reusable slider component
├── types/
│   └── sdxl.ts                  # TypeScript interfaces
└── utils/
    └── storage.ts               # localStorage utilities
```

## 🔧 Backend Integration

The current implementation includes a mock API endpoint at `/api/generate`. To integrate with your actual SDXL backend:

1. **Update the API route** (`app/api/generate/route.ts`):
   - Replace the mock implementation with your SDXL service call
   - Update the endpoint URL and authentication as needed

2. **Example integration:**
   ```typescript
   const response = await fetch('YOUR_SDXL_ENDPOINT', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
       'Authorization': 'Bearer YOUR_API_KEY',
     },
     body: JSON.stringify(parameters),
   });
   ```

## 🎨 Customization

- **Styling**: Built with Tailwind CSS v4 - modify `tailwind.config.js` and component styles
- **Parameters**: Add new SDXL parameters by updating `types/sdxl.ts` and relevant components
- **UI Layout**: Responsive design that works on desktop and mobile

## 📱 Usage

1. **Enter a prompt** describing the image you want to generate
2. **Select dimensions** using the radio buttons (512×512, 768×768, or 1024×1024)
3. **Adjust advanced settings** if needed (click "Advanced Settings" to expand)
4. **Click "Generate Image"** to start the generation process
5. **Download or copy** the generated image using the buttons below the preview

## 🔄 Development

- **Hot reload**: Changes to components automatically refresh the browser
- **TypeScript**: Full type safety with TypeScript interfaces
- **ESLint**: Code linting with Next.js recommended rules

## 📦 Dependencies

- **Next.js 15.3.3**: React framework with App Router
- **React 19**: Latest React with concurrent features
- **Tailwind CSS v4**: Utility-first CSS framework
- **Lucide React**: Beautiful icons
- **TypeScript**: Type safety and better DX
