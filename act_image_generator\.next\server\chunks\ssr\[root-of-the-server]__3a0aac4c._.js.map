{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/types/sdxl.ts"], "sourcesContent": ["export interface SDXLParameters {\n  prompt: string;\n  negative_prompt: string;\n  width: number;\n  height: number;\n  num_inference_steps: number;\n  denoising_end: number;\n  guidance_scale: number;\n  guidance_rescale: number;\n  seed?: number;\n}\n\nexport interface GenerationResponse {\n  success: boolean;\n  image_url?: string;\n  error?: string;\n}\n\nexport interface DimensionOption {\n  label: string;\n  width: number;\n  height: number;\n}\n\nexport const DIMENSION_OPTIONS: DimensionOption[] = [\n  { label: \"512 × 512\", width: 512, height: 512 },\n  { label: \"768 × 768\", width: 768, height: 768 },\n  { label: \"1024 × 1024\", width: 1024, height: 1024 },\n];\n\nexport const DEFAULT_PARAMETERS: SDXLParameters = {\n  prompt: \"\",\n  negative_prompt: \"out of frame, lowres, text, error, cropped, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck, username, watermark, signature\",\n  width: 1024,\n  height: 1024,\n  num_inference_steps: 50,\n  denoising_end: 0.72,\n  guidance_scale: 6.0,\n  guidance_rescale: 0.7,\n};\n"], "names": [], "mappings": ";;;;AAwBO,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAa,OAAO;QAAK,QAAQ;IAAI;IAC9C;QAAE,OAAO;QAAa,OAAO;QAAK,QAAQ;IAAI;IAC9C;QAAE,OAAO;QAAe,OAAO;QAAM,QAAQ;IAAK;CACnD;AAEM,MAAM,qBAAqC;IAChD,QAAQ;IACR,iBAAiB;IACjB,OAAO;IACP,QAAQ;IACR,qBAAqB;IACrB,eAAe;IACf,gBAAgB;IAChB,kBAAkB;AACpB", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/PromptBox.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface PromptBoxProps {\n  label: string;\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  maxLength?: number;\n  rows?: number;\n  className?: string;\n  error?: string;\n}\n\nexport default function PromptBox({\n  label,\n  value,\n  onChange,\n  placeholder,\n  maxLength = 1000,\n  rows = 4,\n  className,\n  error,\n}: PromptBoxProps) {\n  const characterCount = value.length;\n  const isNearLimit = characterCount > maxLength * 0.8;\n  const isOverLimit = characterCount > maxLength;\n\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      <div className=\"flex justify-between items-center\">\n        <label className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n          {label}\n        </label>\n        <span\n          className={cn(\n            \"text-xs font-mono\",\n            isOverLimit\n              ? \"text-red-600 dark:text-red-400\"\n              : isNearLimit\n              ? \"text-yellow-600 dark:text-yellow-400\"\n              : \"text-gray-500 dark:text-gray-400\"\n          )}\n        >\n          {characterCount}/{maxLength}\n        </span>\n      </div>\n      <textarea\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        placeholder={placeholder}\n        rows={rows}\n        maxLength={maxLength}\n        className={cn(\n          \"w-full px-3 py-2 border rounded-lg shadow-sm resize-vertical transition-colors\",\n          \"focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n          \"dark:bg-gray-800 dark:text-white dark:placeholder-gray-400\",\n          error\n            ? \"border-red-300 dark:border-red-600\"\n            : \"border-gray-300 dark:border-gray-600\",\n          isOverLimit && \"border-red-300 dark:border-red-600\"\n        )}\n      />\n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAgBe,SAAS,UAAU,EAChC,KAAK,EACL,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,IAAI,EAChB,OAAO,CAAC,EACR,SAAS,EACT,KAAK,EACU;IACf,MAAM,iBAAiB,MAAM,MAAM;IACnC,MAAM,cAAc,iBAAiB,YAAY;IACjD,MAAM,cAAc,iBAAiB;IAErC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCACd;;;;;;kCAEH,8OAAC;wBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qBACA,cACI,mCACA,cACA,yCACA;;4BAGL;4BAAe;4BAAE;;;;;;;;;;;;;0BAGtB,8OAAC;gBACC,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,aAAa;gBACb,MAAM;gBACN,WAAW;gBACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kFACA,6EACA,8DACA,QACI,uCACA,wCACJ,eAAe;;;;;;YAGlB,uBACC,8OAAC;gBAAE,WAAU;0BAA0C;;;;;;;;;;;;AAI/D", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/ParamSlider.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SliderPrimitive from \"@radix-ui/react-slider\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ParamSliderProps {\n  label: string;\n  value: number;\n  min: number;\n  max: number;\n  step: number;\n  onChange: (value: number) => void;\n  description?: string;\n  className?: string;\n}\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-blue-600 dark:bg-blue-500\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-blue-600 bg-white ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:border-blue-500 dark:bg-gray-950 dark:ring-offset-gray-950 dark:focus-visible:ring-blue-300\" />\n  </SliderPrimitive.Root>\n));\nSlider.displayName = SliderPrimitive.Root.displayName;\n\nexport default function ParamSlider({\n  label,\n  value,\n  min,\n  max,\n  step,\n  onChange,\n  description,\n  className,\n}: ParamSliderProps) {\n  return (\n    <div className={cn(\"space-y-3\", className)}>\n      <div className=\"flex justify-between items-center\">\n        <label className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n          {label}\n        </label>\n        <span className=\"text-sm text-gray-600 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded\">\n          {value}\n        </span>\n      </div>\n      <Slider\n        value={[value]}\n        onValueChange={(values) => onChange(values[0])}\n        min={min}\n        max={max}\n        step={step}\n        className=\"w-full\"\n      />\n      {description && (\n        <p className=\"text-xs text-gray-500 dark:text-gray-400\">{description}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAiBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEtC,SAAS,YAAY,EAClC,KAAK,EACL,KAAK,EACL,GAAG,EACH,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,SAAS,EACQ;IACjB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCACd;;;;;;kCAEH,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;0BAGL,8OAAC;gBACC,OAAO;oBAAC;iBAAM;gBACd,eAAe,CAAC,SAAW,SAAS,MAAM,CAAC,EAAE;gBAC7C,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,WAAU;;;;;;YAEX,6BACC,8OAAC;gBAAE,WAAU;0BAA4C;;;;;;;;;;;;AAIjE", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/GenerateForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useForm } from \"react-hook-form\";\nimport { useState } from \"react\";\nimport { Wand2, ChevronDown, ChevronUp } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { SDXLParameters, DEFAULT_PARAMETERS, DIMENSION_OPTIONS } from \"@/types/sdxl\";\nimport PromptBox from \"./PromptBox\";\nimport ParamSlider from \"./ParamSlider\";\n\ninterface GenerateFormProps {\n  onSubmit: (data: SDXLParameters) => void;\n  isLoading: boolean;\n  className?: string;\n}\n\nexport default function GenerateForm({ \n  onSubmit, \n  isLoading, \n  className \n}: GenerateFormProps) {\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  \n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors },\n  } = useForm<SDXLParameters>({\n    defaultValues: DEFAULT_PARAMETERS,\n  });\n\n  const watchedValues = watch();\n\n  const handleFormSubmit = (data: SDXLParameters) => {\n    onSubmit(data);\n  };\n\n  return (\n    <div className={cn(\n      \"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-lg p-6\",\n      className\n    )}>\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        Generation Parameters\n      </h2>\n      \n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n        {/* Prompt */}\n        <PromptBox\n          label=\"Prompt\"\n          value={watchedValues.prompt}\n          onChange={(value) => setValue(\"prompt\", value)}\n          placeholder=\"Describe the image you want to generate...\"\n          maxLength={1000}\n          rows={4}\n          error={errors.prompt?.message}\n        />\n\n        {/* Dimensions */}\n        <div className=\"space-y-3\">\n          <label className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n            Dimensions\n          </label>\n          <div className=\"grid grid-cols-1 gap-2\">\n            {DIMENSION_OPTIONS.map((option) => (\n              <label\n                key={option.label}\n                className=\"flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n              >\n                <input\n                  type=\"radio\"\n                  value={`${option.width}x${option.height}`}\n                  checked={\n                    watchedValues.width === option.width && \n                    watchedValues.height === option.height\n                  }\n                  onChange={() => {\n                    setValue(\"width\", option.width);\n                    setValue(\"height\", option.height);\n                  }}\n                  className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300 font-medium\">\n                  {option.label}\n                </span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Advanced Settings */}\n        <div className=\"border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden\">\n          <button\n            type=\"button\"\n            onClick={() => setShowAdvanced(!showAdvanced)}\n            className=\"w-full px-4 py-3 flex items-center justify-between text-left bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Advanced Settings\n            </span>\n            {showAdvanced ? (\n              <ChevronUp className=\"w-4 h-4 text-gray-500\" />\n            ) : (\n              <ChevronDown className=\"w-4 h-4 text-gray-500\" />\n            )}\n          </button>\n          \n          {showAdvanced && (\n            <div className=\"p-4 space-y-6 border-t border-gray-200 dark:border-gray-700\">\n              {/* Negative Prompt */}\n              <PromptBox\n                label=\"Negative Prompt\"\n                value={watchedValues.negative_prompt}\n                onChange={(value) => setValue(\"negative_prompt\", value)}\n                placeholder=\"Describe what you don't want in the image...\"\n                maxLength={500}\n                rows={3}\n              />\n              \n              {/* Inference Steps */}\n              <ParamSlider\n                label=\"Inference Steps\"\n                value={watchedValues.num_inference_steps}\n                min={20}\n                max={100}\n                step={1}\n                onChange={(value) => setValue(\"num_inference_steps\", value)}\n                description=\"More steps = higher quality but slower generation\"\n              />\n              \n              {/* Denoising End */}\n              <ParamSlider\n                label=\"Denoising End\"\n                value={watchedValues.denoising_end}\n                min={0.6}\n                max={0.9}\n                step={0.01}\n                onChange={(value) => setValue(\"denoising_end\", value)}\n                description=\"Controls when to stop the denoising process\"\n              />\n              \n              {/* Guidance Scale */}\n              <ParamSlider\n                label=\"Guidance Scale\"\n                value={watchedValues.guidance_scale}\n                min={1}\n                max={10}\n                step={0.1}\n                onChange={(value) => setValue(\"guidance_scale\", value)}\n                description=\"How closely to follow the prompt\"\n              />\n              \n              {/* Guidance Rescale */}\n              <ParamSlider\n                label=\"Guidance Rescale\"\n                value={watchedValues.guidance_rescale}\n                min={0.0}\n                max={1.0}\n                step={0.05}\n                onChange={(value) => setValue(\"guidance_rescale\", value)}\n                description=\"Rescales the guidance to prevent over-saturation\"\n              />\n              \n              {/* Seed */}\n              <div className=\"space-y-3\">\n                <label className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                  Seed (optional)\n                </label>\n                <div className=\"flex space-x-2\">\n                  <input\n                    type=\"number\"\n                    {...register(\"seed\", { valueAsNumber: true })}\n                    placeholder=\"Random\"\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setValue(\"seed\", Math.floor(Math.random() * 1000000))}\n                    className=\"px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors duration-200 text-sm font-medium\"\n                  >\n                    Random\n                  </button>\n                </div>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Leave empty for random seed, or enter a number for reproducible results\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Generate Button */}\n        <button\n          type=\"submit\"\n          disabled={isLoading || !watchedValues.prompt.trim()}\n          className=\"w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-colors duration-200 shadow-lg\"\n        >\n          <Wand2 className=\"w-5 h-5\" />\n          <span>{isLoading ? \"Generating...\" : \"Generate Image\"}</span>\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAgBe,SAAS,aAAa,EACnC,QAAQ,EACR,SAAS,EACT,SAAS,EACS;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,eAAe,6GAAA,CAAA,qBAAkB;IACnC;IAEA,MAAM,gBAAgB;IAEtB,MAAM,mBAAmB,CAAC;QACxB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,mGACA;;0BAEA,8OAAC;gBAAG,WAAU;0BAA2D;;;;;;0BAIzE,8OAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,8OAAC,wHAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,cAAc,MAAM;wBAC3B,UAAU,CAAC,QAAU,SAAS,UAAU;wBACxC,aAAY;wBACZ,WAAW;wBACX,MAAM;wBACN,OAAO,OAAO,MAAM,EAAE;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAuD;;;;;;0CAGxE,8OAAC;gCAAI,WAAU;0CACZ,6GAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,uBACtB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDACC,MAAK;gDACL,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE;gDACzC,SACE,cAAc,KAAK,KAAK,OAAO,KAAK,IACpC,cAAc,MAAM,KAAK,OAAO,MAAM;gDAExC,UAAU;oDACR,SAAS,SAAS,OAAO,KAAK;oDAC9B,SAAS,UAAU,OAAO,MAAM;gDAClC;gDACA,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,OAAO,KAAK;;;;;;;uCAjBV,OAAO,KAAK;;;;;;;;;;;;;;;;kCAyBzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;oCAGtE,6BACC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;4BAI1B,8BACC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,wHAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,cAAc,eAAe;wCACpC,UAAU,CAAC,QAAU,SAAS,mBAAmB;wCACjD,aAAY;wCACZ,WAAW;wCACX,MAAM;;;;;;kDAIR,8OAAC,0HAAA,CAAA,UAAW;wCACV,OAAM;wCACN,OAAO,cAAc,mBAAmB;wCACxC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,UAAU,CAAC,QAAU,SAAS,uBAAuB;wCACrD,aAAY;;;;;;kDAId,8OAAC,0HAAA,CAAA,UAAW;wCACV,OAAM;wCACN,OAAO,cAAc,aAAa;wCAClC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,UAAU,CAAC,QAAU,SAAS,iBAAiB;wCAC/C,aAAY;;;;;;kDAId,8OAAC,0HAAA,CAAA,UAAW;wCACV,OAAM;wCACN,OAAO,cAAc,cAAc;wCACnC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,UAAU,CAAC,QAAU,SAAS,kBAAkB;wCAChD,aAAY;;;;;;kDAId,8OAAC,0HAAA,CAAA,UAAW;wCACV,OAAM;wCACN,OAAO,cAAc,gBAAgB;wCACrC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,UAAU,CAAC,QAAU,SAAS,oBAAoB;wCAClD,aAAY;;;;;;kDAId,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAuD;;;;;;0DAGxE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,QAAQ;4DAAE,eAAe;wDAAK,EAAE;wDAC7C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,SAAS,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;wDAC3D,WAAU;kEACX;;;;;;;;;;;;0DAIH,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;kCAShE,8OAAC;wBACC,MAAK;wBACL,UAAU,aAAa,CAAC,cAAc,MAAM,CAAC,IAAI;wBACjD,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAM,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAK/C", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  text?: string;\n}\n\nexport default function LoadingSpinner({ size = \"md\", text }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"w-4 h-4\",\n    md: \"w-8 h-8\",\n    lg: \"w-12 h-12\",\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center space-y-2\">\n      <div\n        className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}\n      />\n      {text && (\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">{text}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAKe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,IAAI,EAAuB;IAC/E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;YAEvG,sBACC,8OAAC;gBAAE,WAAU;0BAA4C;;;;;;;;;;;;AAIjE", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/ImagePreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Download, Copy, Check, Image as ImageIcon } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport LoadingSpinner from \"./LoadingSpinner\";\n\ninterface ImagePreviewProps {\n  imageUrl?: string;\n  isLoading: boolean;\n  error?: string;\n  className?: string;\n}\n\nexport default function ImagePreview({\n  imageUrl,\n  isLoading,\n  error,\n  className\n}: ImagePreviewProps) {\n  const [copied, setCopied] = useState(false);\n\n  const handleDownload = async () => {\n    if (!imageUrl) return;\n\n    try {\n      const response = await fetch(imageUrl);\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `generated-image-${Date.now()}.png`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (error) {\n      console.error(\"Failed to download image:\", error);\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!imageUrl) return;\n\n    try {\n      const response = await fetch(imageUrl);\n      const blob = await response.blob();\n      await navigator.clipboard.write([\n        new ClipboardItem({ \"image/png\": blob })\n      ]);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error(\"Failed to copy image:\", error);\n    }\n  };\n\n  return (\n    <div className={cn(\n      \"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-lg p-6\",\n      className\n    )}>\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n        Generated Image\n      </h2>\n\n      <div className=\"aspect-square bg-gray-50 dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700 flex items-center justify-center overflow-hidden\">\n        {isLoading ? (\n          <LoadingSpinner size=\"lg\" text=\"Generating image...\" />\n        ) : error ? (\n          <div className=\"text-center p-6\">\n            <ImageIcon className=\"w-12 h-12 text-red-400 mx-auto mb-3\" />\n            <p className=\"text-red-600 dark:text-red-400 text-sm\">{error}</p>\n          </div>\n        ) : imageUrl ? (\n          <img\n            src={imageUrl}\n            alt=\"Generated image\"\n            className=\"w-full h-full object-contain rounded-lg\"\n          />\n        ) : (\n          <div className=\"text-center p-6\">\n            <ImageIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-3\" />\n            <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n              Your generated image will appear here\n            </p>\n          </div>\n        )}\n      </div>\n\n      {imageUrl && !isLoading && !error && (\n        <div className=\"flex space-x-3 mt-6\">\n          <button\n            onClick={handleDownload}\n            className=\"flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm\"\n          >\n            <Download className=\"w-4 h-4\" />\n            <span>Download</span>\n          </button>\n\n          <button\n            onClick={handleCopyToClipboard}\n            className=\"flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm\"\n          >\n            {copied ? (\n              <>\n                <Check className=\"w-4 h-4\" />\n                <span>Copied!</span>\n              </>\n            ) : (\n              <>\n                <Copy className=\"w-4 h-4\" />\n                <span>Copy</span>\n              </>\n            )}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAce,SAAS,aAAa,EACnC,QAAQ,EACR,SAAS,EACT,KAAK,EACL,SAAS,EACS;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;YAChD,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;gBAC9B,IAAI,cAAc;oBAAE,aAAa;gBAAK;aACvC;YACD,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,mGACA;;0BAEA,8OAAC;gBAAG,WAAU;0BAA2D;;;;;;0BAIzE,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC,6HAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,MAAK;;;;;2BAC7B,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;2BAEvD,yBACF,8OAAC;oBACC,KAAK;oBACL,KAAI;oBACJ,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;;;;;;YAO7D,YAAY,CAAC,aAAa,CAAC,uBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;kCAGR,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAET,uBACC;;8CACE,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;yDAGR;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/components/ErrorToast.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as ToastPrimitive from \"@radix-ui/react-toast\";\nimport { X, AlertCircle } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst ToastProvider = ToastPrimitive.Provider;\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitive.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitive.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitive.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n));\nToastViewport.displayName = ToastPrimitive.Viewport.displayName;\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <ToastPrimitive.Root\n      ref={ref}\n      className={cn(\n        \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-lg border border-red-200 bg-red-50 p-4 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full dark:border-red-800 dark:bg-red-950\",\n        className\n      )}\n      {...props}\n    />\n  );\n});\nToast.displayName = ToastPrimitive.Root.displayName;\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitive.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-red-300 bg-transparent px-3 text-sm font-medium ring-offset-white transition-colors hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-red-500 group-[.destructive]:hover:border-red-600 group-[.destructive]:hover:bg-red-600 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-600 dark:ring-offset-red-950 dark:border-red-700 dark:hover:bg-red-800 dark:focus:ring-red-300\",\n      className\n    )}\n    {...props}\n  />\n));\nToastAction.displayName = ToastPrimitive.Action.displayName;\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitive.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitive.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitive.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-red-600 opacity-0 transition-opacity hover:text-red-900 focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600 dark:text-red-400 dark:hover:text-red-100\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitive.Close>\n));\nToastClose.displayName = ToastPrimitive.Close.displayName;\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitive.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold text-red-900 dark:text-red-100\", className)}\n    {...props}\n  />\n));\nToastTitle.displayName = ToastPrimitive.Title.displayName;\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-red-700 dark:text-red-300\", className)}\n    {...props}\n  />\n));\nToastDescription.displayName = ToastPrimitive.Description.displayName;\n\ninterface ErrorToastProps {\n  title?: string;\n  description: string;\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport default function ErrorToast({\n  title = \"Error\",\n  description,\n  open,\n  onOpenChange,\n}: ErrorToastProps) {\n  return (\n    <ToastProvider swipeDirection=\"right\">\n      <Toast open={open} onOpenChange={onOpenChange}>\n        <div className=\"flex items-start space-x-3\">\n          <AlertCircle className=\"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0\" />\n          <div className=\"flex-1\">\n            <ToastTitle>{title}</ToastTitle>\n            <ToastDescription>{description}</ToastDescription>\n          </div>\n        </div>\n        <ToastClose />\n      </Toast>\n      <ToastViewport />\n    </ToastProvider>\n  );\n}\n\nexport {\n  type ErrorToastProps,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,gBAAgB,iKAAA,CAAA,WAAuB;AAE7C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,WAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,iKAAA,CAAA,WAAuB,CAAC,WAAW;AAE/D,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0pBACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW;AAEnD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,SAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4kBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAA,CAAA,SAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0XACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,iKAAA,CAAA,cAA0B,CAAC,WAAW;AAStD,SAAS,WAAW,EACjC,QAAQ,OAAO,EACf,WAAW,EACX,IAAI,EACJ,YAAY,EACI;IAChB,qBACE,8OAAC;QAAc,gBAAe;;0BAC5B,8OAAC;gBAAM,MAAM;gBAAM,cAAc;;kCAC/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAY;;;;;;kDACb,8OAAC;kDAAkB;;;;;;;;;;;;;;;;;;kCAGvB,8OAAC;;;;;;;;;;;0BAEH,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/utils/storage.ts"], "sourcesContent": ["import { SDXLParameters, DEFAULT_PARAMETERS } from \"@/types/sdxl\";\n\nconst STORAGE_KEY = \"act_image_generator_parameters\";\n\nexport function saveParametersToStorage(parameters: SDXLParameters): void {\n  if (typeof window !== \"undefined\") {\n    try {\n      localStorage.setItem(STORAGE_KEY, JSON.stringify(parameters));\n    } catch (error) {\n      console.error(\"Failed to save parameters to localStorage:\", error);\n    }\n  }\n}\n\nexport function loadParametersFromStorage(): SDXLParameters {\n  if (typeof window !== \"undefined\") {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEY);\n      if (stored) {\n        const parsed = JSON.parse(stored);\n        // Merge with defaults to ensure all properties exist\n        return { ...DEFAULT_PARAMETERS, ...parsed };\n      }\n    } catch (error) {\n      console.error(\"Failed to load parameters from localStorage:\", error);\n    }\n  }\n  return DEFAULT_PARAMETERS;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AAEb,SAAS,wBAAwB,UAA0B;IAChE,uCAAmC;;IAMnC;AACF;AAEO,SAAS;IACd,uCAAmC;;IAWnC;IACA,OAAO,6GAAA,CAAA,qBAAkB;AAC3B", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureaublad/Docker/ACT/act_image_generator/app/ImageGeneratorClient.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { createPortal } from \"react-dom\";\nimport GenerateForm from \"@/components/GenerateForm\";\nimport ImagePreview from \"@/components/ImagePreview\";\nimport ErrorToast from \"@/components/ErrorToast\";\nimport { SDXLParameters, GenerationResponse } from \"@/types/sdxl\";\nimport { saveParametersToStorage, loadParametersFromStorage } from \"@/utils/storage\";\n\nexport default function ImageGeneratorClient() {\n  const [parameters, setParameters] = useState<SDXLParameters>(() => loadParametersFromStorage());\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generatedImageUrl, setGeneratedImageUrl] = useState<string>();\n  const [error, setError] = useState<string>();\n  const [showErrorToast, setShowErrorToast] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // Handle client-side mounting\n  useEffect(() => {\n    setMounted(true);\n    // Load parameters from localStorage after mounting\n    const savedParams = loadParametersFromStorage();\n    setParameters(savedParams);\n  }, []);\n\n  // Save parameters to localStorage whenever they change\n  useEffect(() => {\n    if (mounted) {\n      saveParametersToStorage(parameters);\n    }\n  }, [parameters, mounted]);\n\n  // Show error toast when error changes\n  useEffect(() => {\n    if (error) {\n      setShowErrorToast(true);\n    }\n  }, [error]);\n\n  const handleGenerate = async (formData: SDXLParameters) => {\n    if (!formData.prompt.trim()) {\n      setError(\"Please enter a prompt\");\n      return;\n    }\n\n    setIsGenerating(true);\n    setError(undefined);\n    setGeneratedImageUrl(undefined);\n\n    try {\n      const response = await fetch(\"/api/generate\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result: GenerationResponse = await response.json();\n\n      if (result.success && result.image_url) {\n        setGeneratedImageUrl(result.image_url);\n        setParameters(formData); // Update parameters with successful generation\n      } else {\n        setError(result.error || \"Failed to generate image\");\n      }\n    } catch (err) {\n      setError(\"Network error. Please try again.\");\n      console.error(\"Generation error:\", err);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const handleErrorToastClose = () => {\n    setShowErrorToast(false);\n    setError(undefined);\n  };\n\n  // Don't render until mounted to avoid hydration issues\n  if (!mounted) {\n    return (\n      <div className=\"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-lg p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6\"></div>\n          <div className=\"space-y-4\">\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"></div>\n            <div className=\"h-20 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"></div>\n            <div className=\"space-y-2\">\n              <div className=\"h-12 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n              <div className=\"h-12 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n              <div className=\"h-12 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const imagePreviewContainer = document.getElementById(\"image-preview-container\");\n\n  return (\n    <>\n      <GenerateForm\n        onSubmit={handleGenerate}\n        isLoading={isGenerating}\n      />\n      \n      {/* Portal the ImagePreview to the right column */}\n      {imagePreviewContainer && createPortal(\n        <ImagePreview\n          imageUrl={generatedImageUrl}\n          isLoading={isGenerating}\n          error={error}\n        />,\n        imagePreviewContainer\n      )}\n\n      {/* Error Toast */}\n      <ErrorToast\n        description={error || \"\"}\n        open={showErrorToast}\n        onOpenChange={handleErrorToastClose}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,IAAM,CAAA,GAAA,gHAAA,CAAA,4BAAyB,AAAD;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,mDAAmD;QACnD,MAAM,cAAc,CAAA,GAAA,gHAAA,CAAA,4BAAyB,AAAD;QAC5C,cAAc;IAChB,GAAG,EAAE;IAEL,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,CAAA,GAAA,gHAAA,CAAA,0BAAuB,AAAD,EAAE;QAC1B;IACF,GAAG;QAAC;QAAY;KAAQ;IAExB,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,kBAAkB;QACpB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI;YAC3B,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,OAAO,OAAO,IAAI,OAAO,SAAS,EAAE;gBACtC,qBAAqB,OAAO,SAAS;gBACrC,cAAc,WAAW,+CAA+C;YAC1E,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;QAClB,SAAS;IACX;IAEA,uDAAuD;IACvD,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,MAAM,wBAAwB,SAAS,cAAc,CAAC;IAEtD,qBACE;;0BACE,8OAAC,2HAAA,CAAA,UAAY;gBACX,UAAU;gBACV,WAAW;;;;;;YAIZ,uCAAyB,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACnC,8OAAC,2HAAA,CAAA,UAAY;gBACX,UAAU;gBACV,WAAW;gBACX,OAAO;;;;;sBAET;0BAIF,8OAAC,yHAAA,CAAA,UAAU;gBACT,aAAa,SAAS;gBACtB,MAAM;gBACN,cAAc;;;;;;;;AAItB", "debugId": null}}]}