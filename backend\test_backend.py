#!/usr/bin/env python3
"""
Test script for ACT Image Generator Backend.
Tests both health check and image generation endpoints.
"""

import json
import time
import base64
import requests
from pathlib import Path


def test_health_endpoint(base_url: str = "http://localhost:8000"):
    """Test the health check endpoint."""
    print("🔍 Testing health endpoint...")
    
    try:
        response = requests.get(f"{base_url}/healthz", timeout=10)
        response.raise_for_status()
        
        health_data = response.json()
        print(f"✅ Health check passed")
        print(f"   Status: {health_data.get('status')}")
        print(f"   CUDA Available: {health_data.get('cuda_available')}")
        print(f"   GPU Device: {health_data.get('cuda_device_name')}")
        print(f"   Memory Allocated: {health_data.get('memory_allocated_mb', 0):.1f} MB")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False


def test_generation_endpoint(base_url: str = "http://localhost:8000"):
    """Test the image generation endpoint."""
    print("\n🎨 Testing image generation...")
    
    # Test request
    test_request = {
        "prompt": "portrait photo of a professional businessman in a modern office, high quality, detailed",
        "negative_prompt": "blurry, low quality, distorted, cartoon",
        "width": 1024,
        "height": 1024,
        "steps": 30,  # Reduced for faster testing
        "denoising_end": 0.72,
        "guidance_scale": 6.0,
        "guidance_rescale": 0.7,
        "seed": 42
    }
    
    try:
        print(f"   Prompt: {test_request['prompt'][:50]}...")
        print(f"   Dimensions: {test_request['width']}x{test_request['height']}")
        print(f"   Steps: {test_request['steps']}")
        
        start_time = time.time()
        
        response = requests.post(
            f"{base_url}/generate",
            json=test_request,
            timeout=180  # 3 minutes timeout
        )
        response.raise_for_status()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        result = response.json()
        
        if result.get("success"):
            print(f"✅ Generation successful!")
            print(f"   Generation ID: {result.get('generation_id')}")
            print(f"   Server Latency: {result.get('latency_ms', 0):.1f} ms")
            print(f"   Total Request Time: {total_time:.1f} s")
            print(f"   Peak VRAM: {result.get('vram_peak_mb', 0):.1f} MB")
            
            # Save image if provided
            if result.get("image"):
                save_test_image(result["image"], result.get("generation_id", "test"))
                
            return True
        else:
            print(f"❌ Generation failed: {result.get('error')}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Generation timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Generation request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def save_test_image(base64_data: str, generation_id: str):
    """Save the generated image to disk."""
    try:
        # Create output directory
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        
        # Decode base64 image
        image_data = base64.b64decode(base64_data)
        
        # Save to file
        output_path = output_dir / f"test_generation_{generation_id}.png"
        with open(output_path, "wb") as f:
            f.write(image_data)
            
        print(f"   💾 Image saved to: {output_path}")
        
    except Exception as e:
        print(f"   ⚠️  Failed to save image: {e}")


def test_validation_errors(base_url: str = "http://localhost:8000"):
    """Test request validation."""
    print("\n🔍 Testing request validation...")
    
    # Test invalid request (missing prompt)
    invalid_request = {
        "width": 1024,
        "height": 1024
    }
    
    try:
        response = requests.post(
            f"{base_url}/generate",
            json=invalid_request,
            timeout=10
        )
        
        if response.status_code == 422:
            print("✅ Validation correctly rejected invalid request")
            return True
        else:
            print(f"❌ Expected 422 validation error, got {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Validation test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting ACT Image Generator Backend Tests")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test health endpoint
    health_ok = test_health_endpoint(base_url)
    
    if not health_ok:
        print("\n❌ Backend is not healthy. Please check the server.")
        return
    
    # Test validation
    validation_ok = test_validation_errors(base_url)
    
    # Test generation (only if health check passed)
    generation_ok = test_generation_endpoint(base_url)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Health Check: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"   Validation: {'✅ PASS' if validation_ok else '❌ FAIL'}")
    print(f"   Generation: {'✅ PASS' if generation_ok else '❌ FAIL'}")
    
    if health_ok and validation_ok and generation_ok:
        print("\n🎉 All tests passed! Backend is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the backend configuration.")


if __name__ == "__main__":
    main()
